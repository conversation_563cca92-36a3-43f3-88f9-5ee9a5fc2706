
import { supabase } from '@/lib/supabase';
import { toast } from '@/components/ui/use-toast';

export abstract class BaseService {
  protected supabase = supabase;
  
  protected showError(message: string): void {
    console.error(`[Service Error] ${message}`);
    toast({
      title: '<PERSON>rro',
      description: message,
      variant: 'destructive'
    });
  }
  
  protected showSuccess(message: string): void {
    toast({
      title: 'Sucesso',
      description: message
    });
  }
  
  protected async getCurrentUser() {
    const {
      data: { session },
      error
    } = await this.supabase.auth.getSession();

    const user = session?.user || null;

    if (error || !user) {
      throw new Error('Usuário não autenticado');
    }

    return user;
  }
  
  protected formatPhone(phone: string | null): string | null {
    if (!phone) return null;
    return phone.replace(/\D/g, '');
  }
}
