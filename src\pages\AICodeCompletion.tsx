/**
 * AI Code Completion Demo Page
 * Demonstrates AI-powered code completion functionality
 */

import React, { useState } from 'react';
import PageContainer from '../components/PageContainer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '../components/ui/tabs';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import { Alert, AlertDescription } from '../components/ui/alert';
import { MonacoEditorWithAI } from '../components/ai-completion/MonacoEditorWithAI';
import { AICompletionSettings } from '../components/ai-completion/AICompletionSettings';
import { CreateTestData } from '../components/debug/CreateTestData';
import { aiCompletionConfig } from '../lib/config/ai-completion-config';
import {
  Code,
  Settings,
  Zap,
  BookOpen,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  Bug
} from 'lucide-react';

const SAMPLE_CODE = {
  typescript: `// TypeScript React Component Example
import React, { useState, useEffect } from 'react';

interface User {
  id: number;
  name: string;
  email: string;
}

export function UserProfile() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch user data
    
  }, []);

  return (
    <div className="user-profile">
      
    </div>
  );
}`,
  
  javascript: `// JavaScript API Service Example
class ApiService {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
  }

  async fetchUsers() {
    // Implement user fetching logic
    
  }

  async createUser(userData) {
    // Implement user creation logic
    
  }
}

export default ApiService;`,

  sql: `-- SQL Query Examples
SELECT 
  u.id,
  u.name,
  u.email,
  COUNT(o.id) as order_count
FROM users u
LEFT JOIN orders o ON u.id = o.user_id
WHERE 
  -- Add your conditions here
  
GROUP BY u.id, u.name, u.email
ORDER BY order_count DESC;`,

  html: `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Code Completion Demo</title>
</head>
<body>
  <div class="container">
    <h1>Welcome to AI Code Completion</h1>
    <!-- Add your content here -->
    
  </div>
</body>
</html>`,
};

export function AICodeCompletion() {
  const [activeTab, setActiveTab] = useState('demo');
  const [selectedLanguage, setSelectedLanguage] = useState<keyof typeof SAMPLE_CODE>('typescript');
  const [code, setCode] = useState(SAMPLE_CODE[selectedLanguage]);
  const [hasApiKey, setHasApiKey] = useState(aiCompletionConfig.hasApiKey());

  // Check API key status
  React.useEffect(() => {
    const unsubscribe = aiCompletionConfig.subscribe(() => {
      setHasApiKey(aiCompletionConfig.hasApiKey());
    });
    return unsubscribe;
  }, []);

  const handleLanguageChange = (language: keyof typeof SAMPLE_CODE) => {
    setSelectedLanguage(language);
    setCode(SAMPLE_CODE[language]);
  };

  const getFileExtension = (language: string) => {
    const extensions = {
      typescript: 'ts',
      javascript: 'js',
      sql: 'sql',
      html: 'html',
    };
    return extensions[language as keyof typeof extensions] || 'txt';
  };

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Zap className="h-8 w-8 text-primary" />
              AI Code Completion
            </h1>
            <p className="text-muted-foreground mt-2">
              Experience AI-powered code completion based on windsurf.vim architecture
            </p>
          </div>
          <div className="flex items-center gap-2">
            {hasApiKey ? (
              <Badge variant="default" className="flex items-center gap-1">
                <CheckCircle className="h-3 w-3" />
                API Key Configured
              </Badge>
            ) : (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                API Key Required
              </Badge>
            )}
          </div>
        </div>

        {/* API Key Warning */}
        {!hasApiKey && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              To use AI code completion, you need to configure your API key in the Settings tab.
              Get your free API key from{' '}
              <a 
                href="https://codeium.com" 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-primary hover:underline inline-flex items-center gap-1"
              >
                Codeium <ExternalLink className="h-3 w-3" />
              </a>
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="demo" className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Demo
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </TabsTrigger>
            <TabsTrigger value="docs" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Documentation
            </TabsTrigger>
            <TabsTrigger value="debug" className="flex items-center gap-2">
              <Bug className="h-4 w-4" />
              Debug
            </TabsTrigger>
          </TabsList>

          <TabsContent value="demo" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Interactive Code Editor</CardTitle>
                <CardDescription>
                  Try typing code and see AI completions in action. Use Tab to accept suggestions.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Language Selector */}
                  <div className="flex gap-2">
                    {Object.keys(SAMPLE_CODE).map((lang) => (
                      <Button
                        key={lang}
                        variant={selectedLanguage === lang ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => handleLanguageChange(lang as keyof typeof SAMPLE_CODE)}
                      >
                        {lang.charAt(0).toUpperCase() + lang.slice(1)}
                      </Button>
                    ))}
                  </div>

                  {/* Code Editor */}
                  <MonacoEditorWithAI
                    value={code}
                    onChange={setCode}
                    language={selectedLanguage}
                    filePath={`example.${getFileExtension(selectedLanguage)}`}
                    height={500}
                    theme="vs-dark"
                    className="border rounded-lg overflow-hidden"
                  />

                  {/* Instructions */}
                  <div className="bg-muted p-4 rounded-lg">
                    <h4 className="font-semibold mb-2">How to use:</h4>
                    <ul className="text-sm space-y-1 text-muted-foreground">
                      <li>• Start typing code and AI suggestions will appear automatically</li>
                      <li>• Press <kbd className="px-1 py-0.5 bg-background rounded text-xs">Tab</kbd> to accept the current suggestion</li>
                      <li>• Use <kbd className="px-1 py-0.5 bg-background rounded text-xs">Ctrl+]</kbd> and <kbd className="px-1 py-0.5 bg-background rounded text-xs">Ctrl+[</kbd> to cycle through suggestions</li>
                      <li>• Press <kbd className="px-1 py-0.5 bg-background rounded text-xs">Ctrl+K</kbd> to accept only the next word</li>
                      <li>• Press <kbd className="px-1 py-0.5 bg-background rounded text-xs">Ctrl+L</kbd> to accept only the next line</li>
                      <li>• Press <kbd className="px-1 py-0.5 bg-background rounded text-xs">Esc</kbd> to dismiss suggestions</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <AICompletionSettings />
          </TabsContent>

          <TabsContent value="debug" className="space-y-4">
            <CreateTestData />
          </TabsContent>

          <TabsContent value="docs" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>AI Code Completion Documentation</CardTitle>
                <CardDescription>
                  Learn how to integrate and use AI-powered code completion in your projects
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Architecture Overview</h3>
                  <p className="text-muted-foreground mb-4">
                    This AI code completion system is based on the windsurf.vim plugin architecture, 
                    adapted for React and TypeScript applications. It provides real-time code suggestions 
                    powered by advanced language models.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Key Features</h3>
                  <ul className="space-y-2 text-muted-foreground">
                    <li>• <strong>Real-time completions:</strong> Get suggestions as you type</li>
                    <li>• <strong>Multi-language support:</strong> Works with TypeScript, JavaScript, SQL, HTML, and more</li>
                    <li>• <strong>Context-aware:</strong> Understands your codebase context for better suggestions</li>
                    <li>• <strong>Configurable:</strong> Customize behavior, file types, and keyboard shortcuts</li>
                    <li>• <strong>Privacy-focused:</strong> Secure API key management</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">Integration Guide</h3>
                  <div className="space-y-4">
                    <div className="bg-muted p-4 rounded-lg">
                      <h4 className="font-medium mb-2">1. Basic Usage</h4>
                      <pre className="text-sm bg-background p-2 rounded overflow-x-auto">
{`import { MonacoEditorWithAI } from './components/ai-completion/MonacoEditorWithAI';

function MyCodeEditor() {
  const [code, setCode] = useState('');
  
  return (
    <MonacoEditorWithAI
      value={code}
      onChange={setCode}
      language="typescript"
      filePath="example.ts"
    />
  );
}`}
                      </pre>
                    </div>

                    <div className="bg-muted p-4 rounded-lg">
                      <h4 className="font-medium mb-2">2. Custom Configuration</h4>
                      <pre className="text-sm bg-background p-2 rounded overflow-x-auto">
{`import { aiCompletionConfig } from './lib/config/ai-completion-config';

// Configure AI completion
aiCompletionConfig.updateConfig({
  enabled: true,
  debounceMs: 200,
  maxSuggestions: 3,
  autoTrigger: true,
});

// Set API key
aiCompletionConfig.setApiKey('your-api-key');`}
                      </pre>
                    </div>

                    <div className="bg-muted p-4 rounded-lg">
                      <h4 className="font-medium mb-2">3. Using the Hook Directly</h4>
                      <pre className="text-sm bg-background p-2 rounded overflow-x-auto">
{`import { useAICompletion } from './hooks/useAICompletion';

function CustomEditor() {
  const { state, actions } = useAICompletion({
    filePath: 'example.ts',
    language: 'typescript',
  });
  
  // Use state.suggestions and actions.requestCompletions
}`}
                      </pre>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-2">API Reference</h3>
                  <p className="text-muted-foreground">
                    For detailed API documentation, see the TypeScript interfaces in the source code:
                  </p>
                  <ul className="mt-2 space-y-1 text-sm text-muted-foreground">
                    <li>• <code>src/lib/services/ai-completion.ts</code> - Core service</li>
                    <li>• <code>src/hooks/useAICompletion.ts</code> - React hook</li>
                    <li>• <code>src/lib/config/ai-completion-config.ts</code> - Configuration</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  );
}
