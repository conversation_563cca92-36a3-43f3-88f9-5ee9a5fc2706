import { Client } from 'pg';

// Configurações de conexão
const config = {
  host: 'aws-0-eu-west-2.pooler.supabase.com',
  port: 6543,
  database: 'postgres',
  user: 'postgres.rsvjnndhbyyxktbczlnk',
  password: 'P+-@@6CUDUJSUpy',
  ssl: {
    rejectUnauthorized: false
  }
};

async function verificarDadosReais() {
  const client = new Client(config);
  
  try {
    console.log('🔌 Conectando ao banco de dados...');
    await client.connect();
    console.log('✅ Conexão estabelecida!\n');

    // 1. Verificar dados da tabela profiles
    console.log('👥 DADOS DA TABELA PROFILES:');
    console.log('=====================================');
    const profilesResult = await client.query(`
      SELECT id, user_id, full_name, email, user_type, status, created_at
      FROM public.profiles 
      ORDER BY created_at DESC;
    `);
    
    if (profilesResult.rows.length > 0) {
      profilesResult.rows.forEach((profile, index) => {
        console.log(`${index + 1}. ${profile.full_name} (${profile.email})`);
        console.log(`   Tipo: ${profile.user_type} | Status: ${profile.status}`);
        console.log(`   ID: ${profile.id} | User ID: ${profile.user_id}`);
        console.log(`   Criado em: ${profile.created_at}`);
        console.log('');
      });
    } else {
      console.log('❌ Nenhum perfil encontrado');
    }

    // 2. Verificar dados da tabela guardians
    console.log('👨‍👩‍👧‍👦 DADOS DA TABELA GUARDIANS:');
    console.log('=====================================');
    const guardiansResult = await client.query(`
      SELECT g.id, g.student_id, g.email, g.full_name, g.is_active, g.created_at,
             p.full_name as student_name, p.email as student_email
      FROM public.guardians g
      LEFT JOIN public.profiles p ON g.student_id = p.user_id
      ORDER BY g.created_at DESC;
    `);
    
    if (guardiansResult.rows.length > 0) {
      guardiansResult.rows.forEach((guardian, index) => {
        console.log(`${index + 1}. Responsável: ${guardian.full_name || 'N/A'} (${guardian.email || 'N/A'})`);
        console.log(`   Estudante: ${guardian.student_name || 'N/A'} (${guardian.student_email || 'N/A'})`);
        console.log(`   Ativo: ${guardian.is_active ? 'Sim' : 'Não'}`);
        console.log(`   ID: ${guardian.id} | Student ID: ${guardian.student_id}`);
        console.log(`   Criado em: ${guardian.created_at}`);
        console.log('');
      });
    } else {
      console.log('❌ Nenhum relacionamento responsável-estudante encontrado');
    }

    // 3. Verificar dados da tabela locations
    console.log('📍 DADOS DA TABELA LOCATIONS:');
    console.log('=====================================');
    const locationsResult = await client.query(`
      SELECT l.id, l.user_id, l.latitude, l.longitude, l.timestamp, l.address, l.shared_with_guardians,
             p.full_name as user_name, p.email as user_email, p.user_type
      FROM public.locations l
      LEFT JOIN public.profiles p ON l.user_id = p.user_id
      ORDER BY l.timestamp DESC
      LIMIT 10;
    `);
    
    if (locationsResult.rows.length > 0) {
      console.log(`📊 Total de localizações: ${locationsResult.rows.length} (mostrando as 10 mais recentes)`);
      locationsResult.rows.forEach((location, index) => {
        console.log(`${index + 1}. ${location.user_name || 'Usuário N/A'} (${location.user_email || 'N/A'})`);
        console.log(`   Coordenadas: ${location.latitude}, ${location.longitude}`);
        console.log(`   Endereço: ${location.address || 'N/A'}`);
        console.log(`   Compartilhado: ${location.shared_with_guardians ? 'Sim' : 'Não'}`);
        console.log(`   Timestamp: ${location.timestamp}`);
        console.log(`   Tipo: ${location.user_type || 'N/A'}`);
        console.log('');
      });
    } else {
      console.log('❌ Nenhuma localização encontrada');
    }

    // 4. Verificar estrutura da tabela students
    console.log('🎓 ESTRUTURA DA TABELA STUDENTS:');
    console.log('=====================================');
    try {
      const studentsStructureResult = await client.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'students'
        ORDER BY ordinal_position;
      `);
      
      if (studentsStructureResult.rows.length > 0) {
        console.log('📋 Colunas da tabela students:');
        studentsStructureResult.rows.forEach(col => {
          console.log(`   - ${col.column_name}: ${col.data_type} ${col.is_nullable === 'YES' ? '(nullable)' : '(not null)'}`);
        });
        
        // Tentar ver dados da tabela students
        const studentsResult = await client.query(`
          SELECT * FROM public.students LIMIT 5;
        `);
        
        if (studentsResult.rows.length > 0) {
          console.log('\n📊 Dados da tabela students:');
          studentsResult.rows.forEach((student, index) => {
            console.log(`${index + 1}. ${JSON.stringify(student)}`);
          });
        } else {
          console.log('\n❌ Nenhum estudante encontrado na tabela students');
        }
      } else {
        console.log('❌ Tabela students não encontrada');
      }
    } catch (error) {
      console.log(`❌ Erro ao verificar tabela students: ${error.message}`);
    }

    // 5. Verificar dados da tabela student_guardian_relationships
    console.log('\n🔗 DADOS DA TABELA STUDENT_GUARDIAN_RELATIONSHIPS:');
    console.log('=====================================');
    try {
      const relationshipsResult = await client.query(`
        SELECT sgr.*, 
               sp.full_name as student_name, sp.email as student_email,
               gp.full_name as guardian_name, gp.email as guardian_email
        FROM public.student_guardian_relationships sgr
        LEFT JOIN public.profiles sp ON sgr.student_id = sp.user_id
        LEFT JOIN public.profiles gp ON sgr.guardian_id = gp.user_id
        ORDER BY sgr.created_at DESC;
      `);
      
      if (relationshipsResult.rows.length > 0) {
        relationshipsResult.rows.forEach((relationship, index) => {
          console.log(`${index + 1}. Estudante: ${relationship.student_name || 'N/A'} (${relationship.student_email || 'N/A'})`);
          console.log(`   Responsável: ${relationship.guardian_name || 'N/A'} (${relationship.guardian_email || 'N/A'})`);
          console.log(`   Status: ${relationship.status || 'N/A'}`);
          console.log(`   Criado em: ${relationship.created_at}`);
          console.log('');
        });
      } else {
        console.log('❌ Nenhum relacionamento encontrado na tabela student_guardian_relationships');
      }
    } catch (error) {
      console.log(`❌ Erro ao verificar relacionamentos: ${error.message}`);
    }

    // 6. Verificar dados da tabela location_history
    console.log('📚 DADOS DA TABELA LOCATION_HISTORY:');
    console.log('=====================================');
    try {
      const historyResult = await client.query(`
        SELECT COUNT(*) as total_records FROM public.location_history;
      `);
      console.log(`📊 Total de registros no histórico: ${historyResult.rows[0].total_records}`);
    } catch (error) {
      console.log(`❌ Erro ao verificar histórico: ${error.message}`);
    }

    // 7. Verificar dados da tabela auth_logs
    console.log('\n📝 DADOS DA TABELA AUTH_LOGS:');
    console.log('=====================================');
    try {
      const authLogsResult = await client.query(`
        SELECT event_type, user_id, created_at
        FROM public.auth_logs
        ORDER BY created_at DESC
        LIMIT 5;
      `);
      
      if (authLogsResult.rows.length > 0) {
        console.log('📊 Últimos 5 logs de autenticação:');
        authLogsResult.rows.forEach((log, index) => {
          console.log(`${index + 1}. ${log.event_type} - User ID: ${log.user_id} - ${log.created_at}`);
        });
      } else {
        console.log('❌ Nenhum log de autenticação encontrado');
      }
    } catch (error) {
      console.log(`❌ Erro ao verificar logs de autenticação: ${error.message}`);
    }

    // 8. Verificar dados da tabela notification_logs
    console.log('\n🔔 DADOS DA TABELA NOTIFICATION_LOGS:');
    console.log('=====================================');
    try {
      const notificationLogsResult = await client.query(`
        SELECT COUNT(*) as total_notifications FROM public.notification_logs;
      `);
      console.log(`📊 Total de notificações enviadas: ${notificationLogsResult.rows[0].total_notifications}`);
    } catch (error) {
      console.log(`❌ Erro ao verificar logs de notificação: ${error.message}`);
    }

    // 9. Verificar dados da tabela account_deletion_requests
    console.log('\n🗑️ DADOS DA TABELA ACCOUNT_DELETION_REQUESTS:');
    console.log('=====================================');
    try {
      const deletionRequestsResult = await client.query(`
        SELECT COUNT(*) as total_requests FROM public.account_deletion_requests;
      `);
      console.log(`📊 Total de solicitações de exclusão: ${deletionRequestsResult.rows[0].total_requests}`);
    } catch (error) {
      console.log(`❌ Erro ao verificar solicitações de exclusão: ${error.message}`);
    }

    // 10. Resumo geral
    console.log('\n📊 RESUMO GERAL DOS DADOS:');
    console.log('=====================================');
    
    const summaryQueries = [
      { name: 'Perfis de usuário', query: 'SELECT COUNT(*) as count FROM public.profiles' },
      { name: 'Relacionamentos responsável-estudante', query: 'SELECT COUNT(*) as count FROM public.guardians' },
      { name: 'Localizações', query: 'SELECT COUNT(*) as count FROM public.locations' },
      { name: 'Estudantes (tabela students)', query: 'SELECT COUNT(*) as count FROM public.students' },
      { name: 'Relacionamentos avançados', query: 'SELECT COUNT(*) as count FROM public.student_guardian_relationships' },
      { name: 'Histórico de localizações', query: 'SELECT COUNT(*) as count FROM public.location_history' },
      { name: 'Logs de autenticação', query: 'SELECT COUNT(*) as count FROM public.auth_logs' },
      { name: 'Logs de notificação', query: 'SELECT COUNT(*) as count FROM public.notification_logs' },
      { name: 'Solicitações de exclusão', query: 'SELECT COUNT(*) as count FROM public.account_deletion_requests' }
    ];

    for (const item of summaryQueries) {
      try {
        const result = await client.query(item.query);
        console.log(`   ${item.name}: ${result.rows[0].count} registros`);
      } catch (error) {
        console.log(`   ${item.name}: Erro ao contar (${error.message})`);
      }
    }

    console.log('\n✅ Verificação de dados concluída!');

  } catch (error) {
    console.error('❌ Erro na verificação:', error.message);
  } finally {
    await client.end();
  }
}

// Executar a verificação
verificarDadosReais();
