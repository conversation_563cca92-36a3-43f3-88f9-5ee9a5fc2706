/**
 * Create Test Data Component
 * Allows creating test guardian-student relationships directly from the UI
 */

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { supabase } from '../../lib/supabase';
import { useUser } from '../../contexts/UnifiedAuthContext';
import { 
  UserPlus, 
  Users, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  AlertTriangle,
  Database,
  TestTube
} from 'lucide-react';

interface TestStudent {
  name: string;
  email: string;
  school: string;
  grade: string;
}

export function CreateTestData() {
  const { user } = useUser();
  const [isCreating, setIsCreating] = useState(false);
  const [result, setResult] = useState<{
    status: 'idle' | 'success' | 'error';
    message?: string;
    details?: any;
  }>({ status: 'idle' });

  const [testStudents] = useState<TestStudent[]>([
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      school: 'Escola Municipal São Paulo',
      grade: '8º Ano'
    },
    {
      name: 'Maria Santos Lima',
      email: '<EMAIL>',
      school: 'Escola Estadual Maria José',
      grade: '9º Ano'
    }
  ]);

  const createTestData = async () => {
    if (!user) {
      setResult({
        status: 'error',
        message: 'Usuário não autenticado'
      });
      return;
    }

    setIsCreating(true);
    setResult({ status: 'idle' });

    try {
      const createdStudents = [];

      for (const student of testStudents) {
        // 1. Create auth user for student
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          email: student.email,
          email_confirm: true,
          user_metadata: {
            user_type: 'student',
            full_name: student.name
          }
        });

        if (authError) {
          console.error('Error creating auth user:', authError);
          continue;
        }

        const studentUserId = authData.user?.id;
        if (!studentUserId) continue;

        // 2. Create profile for student
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            user_id: studentUserId,
            email: student.email,
            full_name: student.name,
            user_type: 'student',
            status: 'active',
            cpf: `000.000.000-${String(Math.floor(Math.random() * 100)).padStart(2, '0')}` // Generate fake CPF for testing
          });

        if (profileError) {
          console.error('Error creating profile:', profileError);
          continue;
        }

        // 3. Create guardian relationship
        const { error: guardianError } = await supabase
          .from('guardians')
          .insert({
            student_id: studentUserId,
            email: user.email || '',
            full_name: user.user_metadata?.full_name || 'Guardian',
            is_active: true
          });

        if (guardianError) {
          console.error('Error creating guardian relationship:', guardianError);
          continue;
        }

        // 4. Create student record if students table exists
        const { error: studentError } = await supabase
          .from('students')
          .insert({
            id: studentUserId,
            name: student.name,
            school_id: 'TEST001',
            school_name: student.school,
            grade: student.grade,
            class: 'A',
            guardian_id: user.id,
            location_sharing: true
          });

        // Don't fail if students table doesn't exist
        if (studentError && !studentError.message.includes('relation "students" does not exist')) {
          console.error('Error creating student record:', studentError);
        }

        // 5. Create sample location
        const { error: locationError } = await supabase
          .from('locations')
          .insert({
            user_id: studentUserId,
            latitude: -23.5505 + (Math.random() - 0.5) * 0.01,
            longitude: -46.6333 + (Math.random() - 0.5) * 0.01,
            address: `${student.school} - São Paulo, SP`,
            timestamp: new Date().toISOString(),
            shared_with_guardians: true
          });

        if (locationError) {
          console.error('Error creating location:', locationError);
        }

        createdStudents.push({
          id: studentUserId,
          name: student.name,
          email: student.email
        });
      }

      if (createdStudents.length > 0) {
        setResult({
          status: 'success',
          message: `Criados ${createdStudents.length} estudantes de teste com sucesso!`,
          details: createdStudents
        });
      } else {
        setResult({
          status: 'error',
          message: 'Nenhum estudante foi criado. Verifique os logs do console.'
        });
      }

    } catch (error) {
      console.error('Error creating test data:', error);
      setResult({
        status: 'error',
        message: `Erro ao criar dados de teste: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    } finally {
      setIsCreating(false);
    }
  };

  const testRPCFunction = async () => {
    try {
      setResult({ status: 'idle' });
      
      const { data, error } = await supabase.rpc('get_guardian_students');
      
      if (error) {
        setResult({
          status: 'error',
          message: `Erro na função RPC: ${error.message}`,
          details: error
        });
      } else {
        setResult({
          status: 'success',
          message: `Função RPC retornou ${data?.length || 0} estudantes`,
          details: data
        });
      }
    } catch (error) {
      setResult({
        status: 'error',
        message: `Erro ao testar RPC: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    }
  };

  const clearTestData = async () => {
    if (!user) return;

    setIsCreating(true);
    try {
      // Delete guardian relationships
      await supabase
        .from('guardians')
        .delete()
        .eq('email', user.email || '');

      // Delete test profiles
      for (const student of testStudents) {
        const { data: userData } = await supabase.auth.admin.listUsers();
        const testUser = userData.users.find(u => u.email === student.email);
        
        if (testUser) {
          await supabase.auth.admin.deleteUser(testUser.id);
        }
      }

      setResult({
        status: 'success',
        message: 'Dados de teste removidos com sucesso!'
      });
    } catch (error) {
      setResult({
        status: 'error',
        message: `Erro ao remover dados de teste: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Criar Dados de Teste
          </CardTitle>
          <CardDescription>
            Crie estudantes de teste para verificar o funcionamento do dashboard do responsável
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current User Info */}
          <div className="bg-muted p-3 rounded-lg">
            <h4 className="font-medium mb-2">Usuário Atual (Responsável)</h4>
            <div className="text-sm space-y-1">
              <div><strong>Email:</strong> {user?.email}</div>
              <div><strong>ID:</strong> {user?.id}</div>
              <div><strong>Nome:</strong> {user?.user_metadata?.full_name || 'Não definido'}</div>
            </div>
          </div>

          {/* Students to Create */}
          <div>
            <h4 className="font-medium mb-2">Estudantes que serão criados:</h4>
            <div className="space-y-2">
              {testStudents.map((student, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded">
                  <Users className="h-4 w-4" />
                  <div className="flex-1">
                    <div className="font-medium">{student.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {student.email} • {student.school} • {student.grade}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              onClick={createTestData}
              disabled={isCreating}
              className="flex-1"
            >
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Criando...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Criar Dados de Teste
                </>
              )}
            </Button>

            <Button
              variant="outline"
              onClick={testRPCFunction}
              disabled={isCreating}
            >
              <Database className="h-4 w-4 mr-2" />
              Testar RPC
            </Button>

            <Button
              variant="destructive"
              onClick={clearTestData}
              disabled={isCreating}
            >
              Limpar Dados
            </Button>
          </div>

          {/* Result */}
          {result.status !== 'idle' && (
            <Alert variant={result.status === 'error' ? 'destructive' : 'default'}>
              <div className="flex items-start gap-2">
                {result.status === 'success' ? (
                  <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600 mt-0.5" />
                )}
                <div className="flex-1">
                  <AlertDescription>
                    {result.message}
                    {result.details && (
                      <details className="mt-2">
                        <summary className="cursor-pointer text-sm font-medium">
                          Ver detalhes
                        </summary>
                        <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto">
                          {JSON.stringify(result.details, null, 2)}
                        </pre>
                      </details>
                    )}
                  </AlertDescription>
                </div>
              </div>
            </Alert>
          )}

          {/* Instructions */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Como usar:</strong>
              <ol className="list-decimal list-inside mt-2 space-y-1 text-sm">
                <li>Clique em "Criar Dados de Teste" para criar 2 estudantes vinculados ao seu usuário</li>
                <li>Clique em "Testar RPC" para verificar se a função get_guardian_students funciona</li>
                <li>Recarregue o dashboard do responsável para ver os estudantes</li>
                <li>Use "Limpar Dados" para remover os dados de teste quando não precisar mais</li>
              </ol>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
}
