-- <PERSON>ript para criar dados de teste para o dashboard do responsável
-- Execute este script no SQL Editor do Supabase Dashboard

-- 1. <PERSON><PERSON>, vamos verificar se a função get_guardian_students existe
-- Se não existir, vamos criá-la

DROP FUNCTION IF EXISTS public.get_guardian_students();

CREATE OR REPLACE FUNCTION public.get_guardian_students()
RETURNS TABLE (
  student_id UUID,
  student_email TEXT,
  student_name TEXT
) 
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    u.id::UUID as student_id,
    u.email::TEXT as student_email,
    COALESCE(p.full_name, u.email, 'Estudante')::TEXT as student_name
  FROM
    public.guardians g
    JOIN auth.users u ON g.student_id = u.id
    LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE
    g.email = (auth.jwt() ->> 'email')
    AND g.is_active = true;
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION public.get_guardian_students TO authenticated;

-- 2. <PERSON>riar usuários de teste (se não existirem)
-- Nota: Estes usuários precisam ser criados via auth.users, mas vamos criar os profiles

-- Inserir perfis de teste (assumindo que os usuários já existem no auth.users)
INSERT INTO public.profiles (user_id, email, full_name, user_type, created_at, updated_at)
VALUES 
  -- Estudante 1
  (gen_random_uuid(), '<EMAIL>', 'João Silva', 'student', now(), now()),
  -- Estudante 2
  (gen_random_uuid(), '<EMAIL>', 'Maria Santos', 'student', now(), now()),
  -- Responsável
  (gen_random_uuid(), '<EMAIL>', 'Ana Silva', 'parent', now(), now())
ON CONFLICT (user_id) DO NOTHING;

-- 3. Criar relacionamentos guardian-student
-- Primeiro, vamos pegar os IDs dos usuários criados
WITH student1 AS (
  SELECT user_id FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1
),
student2 AS (
  SELECT user_id FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1
)
INSERT INTO public.guardians (student_id, email, full_name, phone, is_active, created_at)
SELECT 
  s.user_id,
  '<EMAIL>',
  'Ana Silva',
  '(11) 99999-9999',
  true,
  now()
FROM student1 s
UNION ALL
SELECT 
  s.user_id,
  '<EMAIL>',
  'Ana Silva',
  '(11) 99999-9999',
  true,
  now()
FROM student2 s
ON CONFLICT DO NOTHING;

-- 4. Criar algumas localizações de teste para os estudantes
WITH student1 AS (
  SELECT user_id FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1
),
student2 AS (
  SELECT user_id FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1
)
INSERT INTO public.locations (user_id, latitude, longitude, address, timestamp, shared_with_guardians, created_at)
SELECT 
  s.user_id,
  -23.5505,
  -46.6333,
  'Escola Municipal São Paulo - SP',
  now() - interval '1 hour',
  true,
  now()
FROM student1 s
UNION ALL
SELECT 
  s.user_id,
  -23.5489,
  -46.6388,
  'Centro de São Paulo - SP',
  now() - interval '30 minutes',
  true,
  now()
FROM student1 s
UNION ALL
SELECT 
  s.user_id,
  -23.5615,
  -46.6565,
  'Escola Estadual Maria José - SP',
  now() - interval '2 hours',
  true,
  now()
FROM student2 s
UNION ALL
SELECT 
  s.user_id,
  -23.5505,
  -46.6333,
  'Biblioteca Municipal - SP',
  now() - interval '45 minutes',
  true,
  now()
FROM student2 s
ON CONFLICT DO NOTHING;

-- 5. Verificar se os dados foram inseridos corretamente
SELECT 'Profiles criados:' as info, count(*) as total FROM public.profiles WHERE email LIKE '%teste.com';
SELECT 'Guardians criados:' as info, count(*) as total FROM public.guardians WHERE email = '<EMAIL>';
SELECT 'Locations criadas:' as info, count(*) as total FROM public.locations WHERE shared_with_guardians = true;

-- 6. Testar a função
SELECT 'Teste da função get_guardian_students:' as info;
-- Esta consulta só funcionará se executada por um usuário autenticado como '<EMAIL>'
-- SELECT * FROM get_guardian_students();

-- 7. Mostrar dados para verificação manual
SELECT 
  'Dados de verificação:' as info,
  p.email as student_email,
  p.full_name as student_name,
  g.email as guardian_email,
  g.full_name as guardian_name
FROM public.profiles p
JOIN public.guardians g ON g.student_id = p.user_id
WHERE p.email LIKE '%teste.com'
ORDER BY p.email;
