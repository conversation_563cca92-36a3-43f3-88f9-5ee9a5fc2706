-- Testar a função get_student_locations_with_names
-- Execute este script no SQL Editor do Supabase

-- 1. Testar com um user_id que sabemos que tem localizações
SELECT * FROM get_student_locations_with_names(
    '864a6c0b-4b17-4df7-8709-0c3f7cf0be91'::UUID, 
    '7 days'::INTERVAL
) LIMIT 5;

-- 2. Testar com outro user_id que também tem localizações
SELECT * FROM get_student_locations_with_names(
    'f9e6bdae-69f0-4f70-a91e-10c4353c7265'::UUID, 
    '7 days'::INTERVAL
) LIMIT 5;

-- 3. Verificar se há relacionamentos de guardian para esses estudantes
SELECT 
    g.student_id,
    g.email as guardian_email,
    g.full_name as guardian_name,
    g.is_active
FROM guardians g
WHERE g.student_id IN (
    '864a6c0b-4b17-4df7-8709-0c3f7cf0be91',
    'f9e6bdae-69f0-4f70-a91e-10c4353c7265'
); 