<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Parent Dashboard</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Parent Dashboard</h1>
        <p>Esta página ajuda a diagnosticar problemas com o dashboard do responsável.</p>
        
        <div>
            <button onclick="testConnection()">1. Testar Conexão</button>
            <button onclick="testAuth()">2. Verificar Autenticação</button>
            <button onclick="testFunction()">3. Testar Função RPC</button>
            <button onclick="testTables()">4. Verificar Tabelas</button>
            <button onclick="runAllTests()">🚀 Executar Todos os Testes</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        // Configuração do Supabase
        const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MDk3NzksImV4cCI6MjA1ODk4NTc3OX0.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${title}</strong>\n${content}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testConnection() {
            addResult('🔗 Testando Conexão...', 'Verificando se conseguimos conectar ao Supabase...');
            
            try {
                const { data, error } = await supabase
                    .from('profiles')
                    .select('count(*)')
                    .limit(1);
                
                if (error) {
                    addResult('❌ Erro na Conexão', `Erro: ${error.message}\nCódigo: ${error.code}`, 'error');
                } else {
                    addResult('✅ Conexão OK', 'Conexão com Supabase estabelecida com sucesso!', 'success');
                }
            } catch (error) {
                addResult('❌ Erro na Conexão', `Erro: ${error.message}`, 'error');
            }
        }
        
        async function testAuth() {
            addResult('🔐 Verificando Autenticação...', 'Verificando estado de autenticação...');
            
            try {
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (error) {
                    addResult('❌ Erro de Autenticação', `Erro: ${error.message}`, 'error');
                } else if (user) {
                    addResult('✅ Usuário Autenticado', `Email: ${user.email}\nID: ${user.id}\nTipo: ${user.user_metadata?.user_type || 'não definido'}`, 'success');
                } else {
                    addResult('ℹ️ Não Autenticado', 'Nenhum usuário autenticado. Faça login primeiro.', 'info');
                }
            } catch (error) {
                addResult('❌ Erro na Verificação', `Erro: ${error.message}`, 'error');
            }
        }
        
        async function testFunction() {
            addResult('🔧 Testando Função RPC...', 'Testando função get_guardian_students...');
            
            try {
                const { data, error } = await supabase.rpc('get_guardian_students');
                
                if (error) {
                    addResult('❌ Erro na Função RPC', 
                        `Erro: ${error.message}\nCódigo: ${error.code}\nDetalhes: ${error.details || 'N/A'}\nHint: ${error.hint || 'N/A'}`, 
                        'error');
                } else {
                    addResult('✅ Função RPC OK', 
                        `Resultado: ${JSON.stringify(data, null, 2)}\nTotal de registros: ${data?.length || 0}`, 
                        'success');
                }
            } catch (error) {
                addResult('❌ Erro na Função RPC', `Erro: ${error.message}`, 'error');
            }
        }
        
        async function testTables() {
            addResult('📊 Verificando Tabelas...', 'Verificando tabelas guardians e profiles...');
            
            // Testar tabela guardians
            try {
                const { data: guardians, error: guardiansError } = await supabase
                    .from('guardians')
                    .select('*')
                    .limit(3);
                
                if (guardiansError) {
                    addResult('❌ Erro na Tabela Guardians', `Erro: ${guardiansError.message}`, 'error');
                } else {
                    addResult('✅ Tabela Guardians', 
                        `Registros encontrados: ${guardians?.length || 0}\nExemplo: ${JSON.stringify(guardians?.[0] || {}, null, 2)}`, 
                        'success');
                }
            } catch (error) {
                addResult('❌ Erro na Tabela Guardians', `Erro: ${error.message}`, 'error');
            }
            
            // Testar tabela profiles
            try {
                const { data: profiles, error: profilesError } = await supabase
                    .from('profiles')
                    .select('user_id, email, full_name, user_type')
                    .eq('user_type', 'student')
                    .limit(3);
                
                if (profilesError) {
                    addResult('❌ Erro na Tabela Profiles', `Erro: ${profilesError.message}`, 'error');
                } else {
                    addResult('✅ Tabela Profiles (Estudantes)', 
                        `Registros encontrados: ${profiles?.length || 0}\nExemplo: ${JSON.stringify(profiles?.[0] || {}, null, 2)}`, 
                        'success');
                }
            } catch (error) {
                addResult('❌ Erro na Tabela Profiles', `Erro: ${error.message}`, 'error');
            }
        }
        
        async function runAllTests() {
            clearResults();
            addResult('🚀 Iniciando Diagnóstico Completo', 'Executando todos os testes...', 'info');
            
            await testConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuth();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testFunction();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTables();
            
            addResult('🎉 Diagnóstico Concluído', 'Todos os testes foram executados. Verifique os resultados acima.', 'info');
        }
        
        // Executar teste básico ao carregar a página
        window.addEventListener('load', () => {
            addResult('📋 Página Carregada', 'Página de debug carregada. Clique nos botões para executar os testes.', 'info');
        });
    </script>
</body>
</html>
