/**
 * Monaco Editor with AI Completion Integration
 * Provides a code editor with AI-powered completions
 */

import React, { useRef, useEffect, useState } from 'react';
import * as monaco from 'monaco-editor';
import { AICompletionProvider, useAICompletionContext } from './AICompletionProvider';
import { Card } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Loader2, Zap, ZapOff } from 'lucide-react';

export interface MonacoEditorWithAIProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  filePath?: string;
  height?: number;
  theme?: 'vs-dark' | 'vs-light';
  readOnly?: boolean;
  className?: string;
}

function MonacoEditorInner({
  value,
  onChange,
  language = 'typescript',
  filePath = 'untitled.ts',
  height = 400,
  theme = 'vs-dark',
  readOnly = false,
  className = '',
}: MonacoEditorWithAIProps) {
  const editorRef = useRef<monaco.editor.IStandaloneCodeEditor | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const { state, actions } = useAICompletionContext();
  const [showSuggestionOverlay, setShowSuggestionOverlay] = useState(false);

  // Initialize Monaco Editor
  useEffect(() => {
    if (!containerRef.current) return;

    const editor = monaco.editor.create(containerRef.current, {
      value,
      language,
      theme,
      readOnly,
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontSize: 14,
      lineNumbers: 'on',
      renderWhitespace: 'selection',
      tabSize: 2,
      insertSpaces: true,
      wordWrap: 'on',
      suggest: {
        // Disable Monaco's built-in suggestions to avoid conflicts
        showKeywords: false,
        showSnippets: false,
        showFunctions: false,
        showConstructors: false,
        showFields: false,
        showVariables: false,
        showClasses: false,
        showStructs: false,
        showInterfaces: false,
        showModules: false,
        showProperties: false,
        showEvents: false,
        showOperators: false,
        showUnits: false,
        showValues: false,
        showConstants: false,
        showEnums: false,
        showEnumMembers: false,
        showColors: false,
        showFiles: false,
        showReferences: false,
        showFolders: false,
        showTypeParameters: false,
        showIssues: false,
        showUsers: false,
      },
    });

    editorRef.current = editor;

    // Handle content changes
    const disposable = editor.onDidChangeModelContent(() => {
      const newValue = editor.getValue();
      onChange(newValue);

      // Request AI completions if enabled and auto-trigger is on
      if (state.isEnabled && state.hasApiKey) {
        const position = editor.getPosition();
        if (position) {
          actions.requestCompletions(newValue, {
            row: position.lineNumber - 1,
            col: position.column - 1,
          });
        }
      }
    });

    // Handle cursor position changes
    const cursorDisposable = editor.onDidChangeCursorPosition(() => {
      if (state.isEnabled && state.hasApiKey) {
        const position = editor.getPosition();
        if (position) {
          actions.requestCompletions(editor.getValue(), {
            row: position.lineNumber - 1,
            col: position.column - 1,
          });
        }
      }
    });

    // Add keyboard shortcuts for AI completion
    editor.addCommand(monaco.KeyCode.Tab, () => {
      if (state.currentSuggestion) {
        const suggestion = actions.acceptCurrentSuggestion();
        if (suggestion) {
          const position = editor.getPosition();
          if (position) {
            editor.executeEdits('ai-completion', [{
              range: new monaco.Range(
                position.lineNumber,
                position.column,
                position.lineNumber,
                position.column
              ),
              text: suggestion,
            }]);
          }
        }
      } else {
        // Default tab behavior
        editor.trigger('keyboard', 'tab', null);
      }
    });

    // Cycle suggestions with Ctrl+]
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.BracketRight, () => {
      actions.cycleSuggestions(1);
    });

    // Cycle suggestions with Ctrl+[
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.BracketLeft, () => {
      actions.cycleSuggestions(-1);
    });

    // Accept next word with Ctrl+K
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyK, () => {
      if (state.currentSuggestion) {
        const word = actions.acceptNextWord();
        if (word) {
          const position = editor.getPosition();
          if (position) {
            editor.executeEdits('ai-completion', [{
              range: new monaco.Range(
                position.lineNumber,
                position.column,
                position.lineNumber,
                position.column
              ),
              text: word,
            }]);
          }
        }
      }
    });

    // Accept next line with Ctrl+L
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyL, () => {
      if (state.currentSuggestion) {
        const line = actions.acceptNextLine();
        if (line) {
          const position = editor.getPosition();
          if (position) {
            editor.executeEdits('ai-completion', [{
              range: new monaco.Range(
                position.lineNumber,
                position.column,
                position.lineNumber,
                position.column
              ),
              text: line,
            }]);
          }
        }
      }
    });

    // Clear suggestions with Escape
    editor.addCommand(monaco.KeyCode.Escape, () => {
      actions.clearSuggestions();
    });

    return () => {
      disposable.dispose();
      cursorDisposable.dispose();
      editor.dispose();
    };
  }, []);

  // Update editor value when prop changes
  useEffect(() => {
    if (editorRef.current && editorRef.current.getValue() !== value) {
      editorRef.current.setValue(value);
    }
  }, [value]);

  // Update editor theme
  useEffect(() => {
    if (editorRef.current) {
      monaco.editor.setTheme(theme);
    }
  }, [theme]);

  // Show/hide suggestion overlay
  useEffect(() => {
    setShowSuggestionOverlay(!!state.currentSuggestion);
  }, [state.currentSuggestion]);

  return (
    <div className={`relative ${className}`}>
      <div
        ref={containerRef}
        style={{ height: `${height}px` }}
        className="border rounded-md overflow-hidden"
      />
      
      {/* AI Completion Status */}
      <div className="absolute top-2 right-2 flex items-center gap-2">
        {state.isLoading && (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Loader2 className="h-3 w-3 animate-spin" />
            AI Thinking...
          </Badge>
        )}
        
        {state.isEnabled ? (
          <Badge variant="default" className="flex items-center gap-1">
            <Zap className="h-3 w-3" />
            AI Enabled
          </Badge>
        ) : (
          <Badge variant="secondary" className="flex items-center gap-1">
            <ZapOff className="h-3 w-3" />
            AI Disabled
          </Badge>
        )}
      </div>

      {/* Suggestion Overlay */}
      {showSuggestionOverlay && state.currentSuggestion && (
        <Card className="absolute bottom-2 left-2 right-2 p-3 bg-background/95 backdrop-blur-sm border shadow-lg">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                AI Suggestion {state.currentSuggestionIndex + 1}/{state.suggestions.length}
              </Badge>
              {state.error && (
                <Badge variant="destructive">Error</Badge>
              )}
            </div>
            <div className="flex gap-1">
              <Button
                size="sm"
                variant="outline"
                onClick={() => actions.cycleSuggestions(-1)}
                disabled={state.suggestions.length <= 1}
              >
                ←
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => actions.cycleSuggestions(1)}
                disabled={state.suggestions.length <= 1}
              >
                →
              </Button>
              <Button
                size="sm"
                onClick={() => {
                  const suggestion = actions.acceptCurrentSuggestion();
                  if (suggestion && editorRef.current) {
                    const position = editorRef.current.getPosition();
                    if (position) {
                      editorRef.current.executeEdits('ai-completion', [{
                        range: new monaco.Range(
                          position.lineNumber,
                          position.column,
                          position.lineNumber,
                          position.column
                        ),
                        text: suggestion,
                      }]);
                    }
                  }
                }}
              >
                Accept
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={actions.clearSuggestions}
              >
                Dismiss
              </Button>
            </div>
          </div>
          
          <div className="text-sm font-mono bg-muted p-2 rounded max-h-32 overflow-y-auto">
            {state.currentSuggestion.completion.text}
          </div>
          
          <div className="text-xs text-muted-foreground mt-2">
            Press Tab to accept • Ctrl+K for next word • Ctrl+L for next line • Esc to dismiss
          </div>
        </Card>
      )}

      {/* Error Display */}
      {state.error && (
        <Card className="absolute bottom-2 left-2 right-2 p-3 bg-destructive/10 border-destructive">
          <div className="text-sm text-destructive">
            AI Completion Error: {state.error}
          </div>
        </Card>
      )}
    </div>
  );
}

export function MonacoEditorWithAI(props: MonacoEditorWithAIProps) {
  return (
    <AICompletionProvider
      options={{
        filePath: props.filePath || 'untitled.ts',
        language: props.language || 'typescript',
        editorOptions: {
          tabSize: 2,
          insertSpaces: true,
        },
        onError: (error) => {
          console.error('AI Completion Error:', error);
        },
        onCompletionAccepted: (completionId) => {
          console.log('Completion accepted:', completionId);
        },
      }}
    >
      <MonacoEditorInner {...props} />
    </AICompletionProvider>
  );
}
