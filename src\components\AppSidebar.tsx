
import { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Home, Users, MapPin, Settings, User, Zap } from 'lucide-react';

export default function AppSidebar() {
  const { user } = useUser();
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems = [
    { icon: Home, label: 'Dashboard', path: user?.user_metadata?.user_type === 'parent' ? '/parent-dashboard' : '/student-dashboard' },
    { icon: MapPin, label: 'Mapa', path: '/student-map' },
    { icon: Users, label: 'Responsáveis', path: '/guardians' },
    { icon: User, label: 'Perfil', path: '/profile' },
    { icon: Settings, label: 'Configurações', path: '/lgpd-settings' },
    { icon: Zap, label: 'AI Code Completion', path: '/ai-completion' },
  ];

  return (
    <aside className={`bg-white shadow-sm border-r transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'}`}>
      <div className="p-4">
        <div className="flex items-center justify-between mb-8">
          {!isCollapsed && <h2 className="text-lg font-semibold">Menu</h2>}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="p-2 rounded-md hover:bg-gray-100"
          >
            <div className="w-4 h-4">
              {isCollapsed ? '→' : '←'}
            </div>
          </button>
        </div>

        <nav className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            return (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center gap-3 px-3 py-2 rounded-md transition-colors ${
                    isActive
                      ? 'bg-blue-50 text-blue-600'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`
                }
              >
                <Icon className="w-5 h-5" />
                {!isCollapsed && <span>{item.label}</span>}
              </NavLink>
            );
          })}
        </nav>
      </div>
    </aside>
  );
}
