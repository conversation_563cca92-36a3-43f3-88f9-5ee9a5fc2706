<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criar <PERSON><PERSON><PERSON><PERSON></title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>👤 Criar Usuário de Teste</h1>
        <p>Esta página permite criar usuários de teste para o sistema.</p>
        
        <form id="userForm">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Senha:</label>
                <input type="password" id="password" value="123456" required>
            </div>
            
            <div class="form-group">
                <label for="fullName">Nome Completo:</label>
                <input type="text" id="fullName" value="Ana Silva" required>
            </div>
            
            <div class="form-group">
                <label for="userType">Tipo de Usuário:</label>
                <select id="userType" required>
                    <option value="parent">Responsável</option>
                    <option value="student">Estudante</option>
                </select>
            </div>
            
            <button type="submit">Criar Usuário</button>
            <button type="button" onclick="loginUser()">Fazer Login</button>
            <button type="button" onclick="checkCurrentUser()">Verificar Usuário Atual</button>
        </form>
        
        <div id="results"></div>
    </div>

    <script>
        // Configuração do Supabase
        const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
        const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MDk3NzksImV4cCI6MjA1ODk4NTc3OX0.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseAnonKey);
        
        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${title}</strong>\n${content}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        document.getElementById('userForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const fullName = document.getElementById('fullName').value;
            const userType = document.getElementById('userType').value;
            
            addResult('👤 Criando Usuário...', `Email: ${email}\nTipo: ${userType}`);
            
            try {
                // Criar usuário
                const { data, error } = await supabase.auth.signUp({
                    email: email,
                    password: password,
                    options: {
                        data: {
                            full_name: fullName,
                            user_type: userType
                        }
                    }
                });
                
                if (error) {
                    addResult('❌ Erro ao Criar Usuário', `Erro: ${error.message}`, 'error');
                } else {
                    addResult('✅ Usuário Criado', 
                        `ID: ${data.user?.id}\nEmail: ${data.user?.email}\nConfirmação necessária: ${!data.user?.email_confirmed_at}`, 
                        'success');
                    
                    // Se o usuário foi criado, tentar criar o perfil
                    if (data.user) {
                        await createProfile(data.user.id, email, fullName, userType);
                    }
                }
            } catch (error) {
                addResult('❌ Erro Geral', `Erro: ${error.message}`, 'error');
            }
        });
        
        async function createProfile(userId, email, fullName, userType) {
            addResult('📝 Criando Perfil...', 'Inserindo dados na tabela profiles...');
            
            try {
                const { data, error } = await supabase
                    .from('profiles')
                    .insert({
                        user_id: userId,
                        email: email,
                        full_name: fullName,
                        user_type: userType
                    });
                
                if (error) {
                    addResult('❌ Erro ao Criar Perfil', `Erro: ${error.message}`, 'error');
                } else {
                    addResult('✅ Perfil Criado', 'Perfil inserido na tabela profiles com sucesso!', 'success');
                }
            } catch (error) {
                addResult('❌ Erro no Perfil', `Erro: ${error.message}`, 'error');
            }
        }
        
        async function loginUser() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            addResult('🔐 Fazendo Login...', `Email: ${email}`);
            
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });
                
                if (error) {
                    addResult('❌ Erro no Login', `Erro: ${error.message}`, 'error');
                } else {
                    addResult('✅ Login Realizado', 
                        `ID: ${data.user?.id}\nEmail: ${data.user?.email}\nTipo: ${data.user?.user_metadata?.user_type}`, 
                        'success');
                    
                    // Redirecionar para o dashboard apropriado
                    const userType = data.user?.user_metadata?.user_type;
                    if (userType === 'parent') {
                        addResult('🔄 Redirecionando...', 'Redirecionando para o dashboard do responsável...');
                        setTimeout(() => {
                            window.location.href = 'http://localhost:4000/parent-dashboard';
                        }, 2000);
                    } else if (userType === 'student') {
                        addResult('🔄 Redirecionando...', 'Redirecionando para o dashboard do estudante...');
                        setTimeout(() => {
                            window.location.href = 'http://localhost:4000/student-dashboard';
                        }, 2000);
                    }
                }
            } catch (error) {
                addResult('❌ Erro no Login', `Erro: ${error.message}`, 'error');
            }
        }
        
        async function checkCurrentUser() {
            addResult('🔍 Verificando Usuário...', 'Verificando usuário atual...');
            
            try {
                const { data: { user }, error } = await supabase.auth.getUser();
                
                if (error) {
                    addResult('❌ Erro na Verificação', `Erro: ${error.message}`, 'error');
                } else if (user) {
                    addResult('✅ Usuário Logado', 
                        `ID: ${user.id}\nEmail: ${user.email}\nTipo: ${user.user_metadata?.user_type || 'não definido'}\nConfirmado: ${user.email_confirmed_at ? 'Sim' : 'Não'}`, 
                        'success');
                } else {
                    addResult('ℹ️ Não Logado', 'Nenhum usuário está logado no momento.');
                }
            } catch (error) {
                addResult('❌ Erro na Verificação', `Erro: ${error.message}`, 'error');
            }
        }
        
        // Verificar usuário atual ao carregar a página
        window.addEventListener('load', checkCurrentUser);
    </script>
</body>
</html>
