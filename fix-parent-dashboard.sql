-- <PERSON><PERSON><PERSON> para corrigir o dashboard do responsável
-- Execute este script no SQL Editor do Supabase Dashboard

-- 1. Criar/corrigir a função get_guardian_students
DROP FUNCTION IF EXISTS public.get_guardian_students();

CREATE OR REPLACE FUNCTION public.get_guardian_students()
RETURNS TABLE (
  student_id UUID,
  student_email TEXT,
  student_name TEXT
) 
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
  -- Log para debug (remover em produção)
  RAISE NOTICE 'get_guardian_students: Executando para usuário %', auth.email();
  
  RETURN QUERY
  SELECT
    u.id::UUID as student_id,
    u.email::TEXT as student_email,
    COALESCE(p.full_name, u.email, 'Estudante')::TEXT as student_name
  FROM
    public.guardians g
    JOIN auth.users u ON g.student_id = u.id
    LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE
    g.email = auth.email()
    AND g.is_active = true;
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION public.get_guardian_students TO authenticated;

-- 2. Verificar se as tabelas existem e criar se necessário
CREATE TABLE IF NOT EXISTS public.guardians (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    student_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    full_name TEXT,
    phone TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Criar índices para performance
CREATE INDEX IF NOT EXISTS idx_guardians_email ON public.guardians(email);
CREATE INDEX IF NOT EXISTS idx_guardians_student_id ON public.guardians(student_id);
CREATE INDEX IF NOT EXISTS idx_guardians_active ON public.guardians(is_active);

-- 3. Habilitar RLS na tabela guardians
ALTER TABLE public.guardians ENABLE ROW LEVEL SECURITY;

-- 4. Criar políticas RLS para a tabela guardians
DROP POLICY IF EXISTS "Guardians can view their own records" ON public.guardians;
CREATE POLICY "Guardians can view their own records" ON public.guardians
    FOR SELECT USING (auth.email() = email);

DROP POLICY IF EXISTS "Guardians can insert their own records" ON public.guardians
    FOR INSERT WITH CHECK (auth.email() = email);

DROP POLICY IF EXISTS "Guardians can update their own records" ON public.guardians
    FOR UPDATE USING (auth.email() = email);

-- 5. Verificar se a tabela profiles existe
CREATE TABLE IF NOT EXISTS public.profiles (
    user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    full_name TEXT,
    user_type TEXT CHECK (user_type IN ('student', 'parent', 'admin', 'developer')),
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);

-- Habilitar RLS na tabela profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Política para profiles
DROP POLICY IF EXISTS "Users can view their own profile" ON public.profiles;
CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = user_id);

-- 6. Verificar se a tabela locations existe
CREATE TABLE IF NOT EXISTS public.locations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    latitude DECIMAL(10, 8) NOT NULL,
    longitude DECIMAL(11, 8) NOT NULL,
    address TEXT,
    timestamp TIMESTAMPTZ DEFAULT now(),
    shared_with_guardians BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Habilitar RLS na tabela locations
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

-- Política para locations
DROP POLICY IF EXISTS "Users can view their own locations" ON public.locations;
CREATE POLICY "Users can view their own locations" ON public.locations
    FOR SELECT USING (auth.uid() = user_id);

-- 7. Criar dados de teste (opcional - descomente se necessário)
/*
-- Inserir um usuário de teste na tabela profiles (assumindo que o usuário já existe em auth.users)
INSERT INTO public.profiles (user_id, email, full_name, user_type)
VALUES 
  ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'João Silva', 'student'),
  ('00000000-0000-0000-0000-000000000002', '<EMAIL>', 'Ana Silva', 'parent')
ON CONFLICT (user_id) DO NOTHING;

-- Inserir relacionamento guardian-student
INSERT INTO public.guardians (student_id, email, full_name, phone, is_active)
VALUES 
  ('00000000-0000-0000-0000-000000000001', '<EMAIL>', 'Ana Silva', '(11) 99999-9999', true)
ON CONFLICT DO NOTHING;

-- Inserir localização de teste
INSERT INTO public.locations (user_id, latitude, longitude, address, shared_with_guardians)
VALUES 
  ('00000000-0000-0000-0000-000000000001', -23.5505, -46.6333, 'Escola Municipal - SP', true)
ON CONFLICT DO NOTHING;
*/

-- 8. Verificações finais
SELECT 'Função get_guardian_students criada com sucesso!' as status;

-- Verificar se as tabelas foram criadas
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN ('guardians', 'profiles', 'locations')
ORDER BY tablename;

-- Verificar se a função foi criada
SELECT 
  proname as function_name,
  proowner::regrole as owner
FROM pg_proc 
WHERE proname = 'get_guardian_students';

-- Instruções para teste
SELECT 'Para testar:' as instrucoes;
SELECT '1. Crie um usuário responsável via auth' as passo_1;
SELECT '2. Crie um usuário estudante via auth' as passo_2;
SELECT '3. Insira dados nas tabelas profiles e guardians' as passo_3;
SELECT '4. Faça login como responsável e acesse /parent-dashboard' as passo_4;
