import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface DebugInfo {
  user: any;
  rpcResult: any;
  rpcError: any;
  guardiansData: any;
  profilesData: any;
  authStatus: any;
}

export const DebugPanel: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    user: null,
    rpcResult: null,
    rpcError: null,
    guardiansData: null,
    profilesData: null,
    authStatus: null
  });
  const [isVisible, setIsVisible] = useState(false);
  const { user } = useUser();

  const runDiagnostics = async () => {
    console.log('🔍 [DebugPanel] Iniciando diagnósticos...');
    
    const newDebugInfo: DebugInfo = {
      user: null,
      rpcResult: null,
      rpcError: null,
      guardiansData: null,
      profilesData: null,
      authStatus: null
    };

    // 1. Verificar usuário atual
    try {
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser();
      newDebugInfo.authStatus = { user: authUser, error: authError };
      newDebugInfo.user = user;
    } catch (error) {
      newDebugInfo.authStatus = { error };
    }

    // 2. Testar função RPC
    try {
      const { data, error } = await supabase.rpc('get_guardian_students');
      newDebugInfo.rpcResult = data;
      newDebugInfo.rpcError = error;
    } catch (error) {
      newDebugInfo.rpcError = error;
    }

    // 3. Verificar tabela guardians
    try {
      const { data, error } = await supabase
        .from('guardians')
        .select('*')
        .limit(5);
      newDebugInfo.guardiansData = { data, error };
    } catch (error) {
      newDebugInfo.guardiansData = { error };
    }

    // 4. Verificar tabela profiles
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_type', 'student')
        .limit(5);
      newDebugInfo.profilesData = { data, error };
    } catch (error) {
      newDebugInfo.profilesData = { error };
    }

    setDebugInfo(newDebugInfo);
  };

  useEffect(() => {
    if (isVisible) {
      runDiagnostics();
    }
  }, [isVisible, user]);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button 
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
        >
          🔧 Debug
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            🔧 Debug Panel - Parent Dashboard
            <Button 
              onClick={() => setIsVisible(false)}
              variant="outline"
              size="sm"
            >
              ✕ Fechar
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={runDiagnostics} className="w-full">
            🔄 Atualizar Diagnósticos
          </Button>

          {/* Status de Autenticação */}
          <div className="border rounded p-3">
            <h3 className="font-bold mb-2">🔐 Status de Autenticação</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify({
                contextUser: user ? {
                  id: user.id,
                  email: user.email,
                  user_type: user.user_metadata?.user_type
                } : null,
                authStatus: debugInfo.authStatus
              }, null, 2)}
            </pre>
          </div>

          {/* Resultado da RPC */}
          <div className="border rounded p-3">
            <h3 className="font-bold mb-2">🔧 Função get_guardian_students</h3>
            <div className="space-y-2">
              <div>
                <strong>Resultado:</strong>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(debugInfo.rpcResult, null, 2)}
                </pre>
              </div>
              {debugInfo.rpcError && (
                <div>
                  <strong className="text-red-600">Erro:</strong>
                  <pre className="text-xs bg-red-100 p-2 rounded overflow-auto">
                    {JSON.stringify(debugInfo.rpcError, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>

          {/* Dados da tabela guardians */}
          <div className="border rounded p-3">
            <h3 className="font-bold mb-2">👥 Tabela Guardians</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(debugInfo.guardiansData, null, 2)}
            </pre>
          </div>

          {/* Dados da tabela profiles */}
          <div className="border rounded p-3">
            <h3 className="font-bold mb-2">👤 Tabela Profiles (Estudantes)</h3>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(debugInfo.profilesData, null, 2)}
            </pre>
          </div>

          {/* Instruções */}
          <div className="border rounded p-3 bg-blue-50">
            <h3 className="font-bold mb-2">📋 Instruções para Correção</h3>
            <ol className="text-sm space-y-1">
              <li>1. Execute o script <code>fix-parent-dashboard.sql</code> no Supabase Dashboard</li>
              <li>2. Crie usuários de teste usando <code>create-test-user.html</code></li>
              <li>3. Insira dados na tabela <code>guardians</code> vinculando responsável e estudante</li>
              <li>4. Faça login como responsável e teste novamente</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DebugPanel;
