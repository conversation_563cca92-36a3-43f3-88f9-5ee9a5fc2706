/**
 * React Hook for AI Code Completion
 * Provides completion functionality for code editors
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  AICompletionService,
  DebouncedCompletionManager,
  CompletionItem,
  EditorOptions
} from '../lib/services/ai-completion';
import { aiCompletionConfig, AICompletionConfig } from '../lib/config/ai-completion-config';

export interface UseAICompletionOptions {
  filePath: string;
  language?: string;
  editorOptions?: EditorOptions;
  onError?: (error: Error) => void;
  onCompletionAccepted?: (completionId: string) => void;
}

export interface AICompletionState {
  isLoading: boolean;
  suggestions: CompletionItem[];
  currentSuggestionIndex: number;
  error: string | null;
  isEnabled: boolean;
  hasApiKey: boolean;
}

export interface AICompletionActions {
  requestCompletions: (content: string, cursorPosition: { row: number; col: number }) => void;
  acceptCurrentSuggestion: () => string | null;
  acceptNextWord: () => string | null;
  acceptNextLine: () => string | null;
  cycleSuggestions: (direction: 1 | -1) => void;
  clearSuggestions: () => void;
  enable: () => void;
  disable: () => void;
  setApiKey: (apiKey: string) => void;
}

export function useAICompletion(options: UseAICompletionOptions) {
  const [state, setState] = useState<AICompletionState>({
    isLoading: false,
    suggestions: [],
    currentSuggestionIndex: 0,
    error: null,
    isEnabled: false,
    hasApiKey: false,
  });

  const serviceRef = useRef<AICompletionService | null>(null);
  const managerRef = useRef<DebouncedCompletionManager | null>(null);
  const configRef = useRef<AICompletionConfig>(aiCompletionConfig.getConfig());

  // Initialize service when API key is available
  useEffect(() => {
    const config = aiCompletionConfig.getConfig();
    const apiKey = aiCompletionConfig.getApiKey();
    
    if (apiKey && config.enabled) {
      serviceRef.current = new AICompletionService(apiKey, config.baseUrl);
      managerRef.current = new DebouncedCompletionManager(
        serviceRef.current,
        config.debounceMs
      );
    } else {
      serviceRef.current = null;
      managerRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isEnabled: config.enabled && !!apiKey && aiCompletionConfig.isEnabledForFileType(
        options.language || 'unspecified'
      ),
      hasApiKey: !!apiKey,
    }));
  }, [options.language]);

  // Subscribe to configuration changes
  useEffect(() => {
    const unsubscribe = aiCompletionConfig.subscribe((config) => {
      configRef.current = config;
      const apiKey = aiCompletionConfig.getApiKey();
      
      // Reinitialize service if needed
      if (apiKey && config.enabled) {
        serviceRef.current = new AICompletionService(apiKey, config.baseUrl);
        managerRef.current = new DebouncedCompletionManager(
          serviceRef.current,
          config.debounceMs
        );
      } else {
        serviceRef.current = null;
        managerRef.current = null;
      }

      setState(prev => ({
        ...prev,
        isEnabled: config.enabled && !!apiKey && aiCompletionConfig.isEnabledForFileType(
          options.language || 'unspecified'
        ),
        hasApiKey: !!apiKey,
      }));
    });

    return unsubscribe;
  }, [options.language]);

  // Request completions
  const requestCompletions = useCallback((
    content: string,
    cursorPosition: { row: number; col: number }
  ) => {
    if (!state.isEnabled || !managerRef.current || !serviceRef.current) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    const documentContext = serviceRef.current.createDocumentContext(
      content,
      options.filePath,
      cursorPosition,
      options.language
    );

    const request = {
      document: documentContext,
      metadata: {
        apiKey: aiCompletionConfig.getApiKey(),
        ideName: 'locate-family-connect',
        ideVersion: '1.0.0',
        extensionName: 'ai-completion',
        extensionVersion: '1.0.0',
      },
      editorOptions: options.editorOptions || {
        tabSize: 2,
        insertSpaces: true,
      },
    };

    managerRef.current.requestCompletions(request, (response, error) => {
      if (error) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error.message,
          suggestions: [],
          currentSuggestionIndex: 0,
        }));
        options.onError?.(error);
        return;
      }

      if (response) {
        const suggestions = response.completionItems.slice(0, configRef.current.maxSuggestions);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: null,
          suggestions,
          currentSuggestionIndex: 0,
        }));
      }
    });
  }, [state.isEnabled, options.filePath, options.language, options.editorOptions, options.onError]);

  // Accept current suggestion
  const acceptCurrentSuggestion = useCallback((): string | null => {
    if (state.suggestions.length === 0 || !serviceRef.current) {
      return null;
    }

    const currentSuggestion = state.suggestions[state.currentSuggestionIndex];
    if (!currentSuggestion) {
      return null;
    }

    // Report acceptance for analytics
    serviceRef.current.acceptCompletion(currentSuggestion.completion.completionId);
    options.onCompletionAccepted?.(currentSuggestion.completion.completionId);

    // Clear suggestions after acceptance
    setState(prev => ({
      ...prev,
      suggestions: [],
      currentSuggestionIndex: 0,
    }));

    return currentSuggestion.completion.text;
  }, [state.suggestions, state.currentSuggestionIndex, options.onCompletionAccepted]);

  // Accept next word from current suggestion
  const acceptNextWord = useCallback((): string | null => {
    if (state.suggestions.length === 0) {
      return null;
    }

    const currentSuggestion = state.suggestions[state.currentSuggestionIndex];
    if (!currentSuggestion) {
      return null;
    }

    const text = currentSuggestion.completion.text;
    const nextWordMatch = text.match(/^\s*\S+/);
    
    return nextWordMatch ? nextWordMatch[0] : null;
  }, [state.suggestions, state.currentSuggestionIndex]);

  // Accept next line from current suggestion
  const acceptNextLine = useCallback((): string | null => {
    if (state.suggestions.length === 0) {
      return null;
    }

    const currentSuggestion = state.suggestions[state.currentSuggestionIndex];
    if (!currentSuggestion) {
      return null;
    }

    const text = currentSuggestion.completion.text;
    const firstLineMatch = text.match(/^[^\n\r]*/);
    
    return firstLineMatch ? firstLineMatch[0] : null;
  }, [state.suggestions, state.currentSuggestionIndex]);

  // Cycle through suggestions
  const cycleSuggestions = useCallback((direction: 1 | -1) => {
    if (state.suggestions.length === 0) {
      return;
    }

    setState(prev => {
      const newIndex = prev.currentSuggestionIndex + direction;
      const maxIndex = prev.suggestions.length - 1;
      
      let nextIndex: number;
      if (newIndex < 0) {
        nextIndex = maxIndex;
      } else if (newIndex > maxIndex) {
        nextIndex = 0;
      } else {
        nextIndex = newIndex;
      }

      return {
        ...prev,
        currentSuggestionIndex: nextIndex,
      };
    });
  }, [state.suggestions.length]);

  // Clear suggestions
  const clearSuggestions = useCallback(() => {
    managerRef.current?.cancel();
    setState(prev => ({
      ...prev,
      suggestions: [],
      currentSuggestionIndex: 0,
      isLoading: false,
      error: null,
    }));
  }, []);

  // Enable completion
  const enable = useCallback(() => {
    aiCompletionConfig.updateConfig({ enabled: true });
  }, []);

  // Disable completion
  const disable = useCallback(() => {
    aiCompletionConfig.updateConfig({ enabled: false });
    clearSuggestions();
  }, [clearSuggestions]);

  // Set API key
  const setApiKey = useCallback(async (apiKey: string) => {
    try {
      const result = await aiCompletionConfig.setApiKey(apiKey);
      if (!result.success) {
        options.onError?.(new Error(result.error || 'Failed to set API key'));
      }
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      options.onError?.(new Error(errorMessage));
      return { success: false, error: errorMessage };
    }
  }, [options.onError]);

  // Get current suggestion
  const currentSuggestion = state.suggestions[state.currentSuggestionIndex] || null;

  return {
    state: {
      ...state,
      currentSuggestion,
    },
    actions: {
      requestCompletions,
      acceptCurrentSuggestion,
      acceptNextWord,
      acceptNextLine,
      cycleSuggestions,
      clearSuggestions,
      enable,
      disable,
      setApiKey,
    },
  };
}
