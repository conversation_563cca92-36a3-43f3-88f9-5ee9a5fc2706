-- <PERSON><PERSON><PERSON> RLS em todas as tabelas relevantes, caso ainda não esteja ativo.
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guardians ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;

--------------------------------------------------------------------------------
-- Políticas para a tabela 'profiles'
--------------------------------------------------------------------------------
-- Remove a política antiga se existir para evitar conflitos.
DROP POLICY IF EXISTS "Authenticated users can view their own profile." ON public.profiles;
-- Cria a política: Usuários autenticados podem ler seus próprios dados de perfil.
-- ISSO CORRIGE O ERRO 403 (FORBIDDEN) ATUAL.
CREATE POLICY "Authenticated users can view their own profile."
ON public.profiles FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

--------------------------------------------------------------------------------
-- Políticas para a tabela 'guardians' (a tabela de vínculos)
--------------------------------------------------------------------------------
-- Permite que um guardião veja os registros que o vinculam aos seus estudantes.
DROP POLICY IF EXISTS "Guardians can view their own student links." ON public.guardians;
CREATE POLICY "Guardians can view their own student links."
ON public.guardians FOR SELECT
TO authenticated
USING (guardian_profile_id = (SELECT id FROM public.guardian_profiles WHERE user_id = auth.uid()));

-- Permite que um estudante veja quem são seus guardiões.
DROP POLICY IF EXISTS "Students can see their own guardian links." ON public.guardians;
CREATE POLICY "Students can see their own guardian links."
ON public.guardians FOR SELECT
TO authenticated
USING (student_id = auth.uid());

--------------------------------------------------------------------------------
-- Políticas para a tabela 'locations'
--------------------------------------------------------------------------------
-- Permite que um estudante insira sua própria localização.
DROP POLICY IF EXISTS "Students can insert their own location." ON public.locations;
CREATE POLICY "Students can insert their own location."
ON public.locations FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

-- Permite que um estudante veja seu próprio histórico de localizações.
DROP POLICY IF EXISTS "Students can view their own locations." ON public.locations;
CREATE POLICY "Students can view their own locations."
ON public.locations FOR SELECT
TO authenticated
USING (user_id = auth.uid());

-- Permite que guardiões vejam o histórico de localização de seus estudantes vinculados.
DROP POLICY IF EXISTS "Guardians can view their students locations." ON public.locations;
CREATE POLICY "Guardians can view their students locations."
ON public.locations FOR SELECT
TO authenticated
USING (
  user_id IN (
    SELECT student_id
    FROM public.guardians
    WHERE guardian_profile_id = (SELECT id FROM public.guardian_profiles WHERE user_id = auth.uid())
  )
); 