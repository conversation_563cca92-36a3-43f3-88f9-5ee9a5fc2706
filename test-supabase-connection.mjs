#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM0MDk3NzksImV4cCI6MjA1ODk4NTc3OX0.AlM_iSptGQ7G5qrJFHU9OECu1wqH6AXQP1zOU70L0T4';

// Inicializar cliente Supabase
const supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('🔧 Testando conexão com Supabase...\n');

async function testConnection() {
  console.log('1. Testando conexão básica...');
  
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('count(*)')
      .limit(1);
    
    if (error) {
      console.log('❌ Erro na conexão:', error.message);
      return false;
    } else {
      console.log('✅ Conexão estabelecida com sucesso');
      return true;
    }
  } catch (error) {
    console.log('❌ Erro na conexão:', error.message);
    return false;
  }
}

async function testGuardianStudentsFunction() {
  console.log('\n2. Testando função get_guardian_students...');
  
  try {
    const { data, error } = await supabase.rpc('get_guardian_students');
    
    if (error) {
      console.log('❌ Erro na função:', error.message);
      console.log('📋 Código do erro:', error.code);
      console.log('📋 Detalhes:', error.details);
      console.log('📋 Hint:', error.hint);
      return false;
    } else {
      console.log('✅ Função executada com sucesso');
      console.log('📊 Resultado:', data);
      return true;
    }
  } catch (error) {
    console.log('❌ Erro ao executar função:', error.message);
    return false;
  }
}

async function checkTables() {
  console.log('\n3. Verificando tabelas...');
  
  // Verificar tabela guardians
  try {
    const { data, error } = await supabase
      .from('guardians')
      .select('*')
      .limit(3);
    
    if (error) {
      console.log('❌ Erro na tabela guardians:', error.message);
    } else {
      console.log(`✅ Tabela guardians: ${data?.length || 0} registros encontrados`);
      if (data && data.length > 0) {
        console.log('📋 Exemplo:', data[0]);
      }
    }
  } catch (error) {
    console.log('❌ Erro ao verificar guardians:', error.message);
  }
  
  // Verificar tabela profiles
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('user_id, email, full_name, user_type')
      .eq('user_type', 'student')
      .limit(3);
    
    if (error) {
      console.log('❌ Erro na tabela profiles:', error.message);
    } else {
      console.log(`✅ Tabela profiles (estudantes): ${data?.length || 0} registros encontrados`);
      if (data && data.length > 0) {
        console.log('📋 Exemplo:', data[0]);
      }
    }
  } catch (error) {
    console.log('❌ Erro ao verificar profiles:', error.message);
  }
}

async function checkAuth() {
  console.log('\n4. Verificando autenticação...');
  
  try {
    const { data: { user }, error } = await supabase.auth.getUser();
    
    if (error) {
      console.log('❌ Erro de autenticação:', error.message);
      console.log('ℹ️  Usuário não autenticado (esperado para teste)');
    } else if (user) {
      console.log('✅ Usuário autenticado:', user.email);
    } else {
      console.log('ℹ️  Nenhum usuário autenticado (esperado para teste)');
    }
  } catch (error) {
    console.log('❌ Erro ao verificar auth:', error.message);
  }
}

// Executar todos os testes
async function main() {
  try {
    const connectionOk = await testConnection();
    
    if (connectionOk) {
      await testGuardianStudentsFunction();
      await checkTables();
      await checkAuth();
    }
    
    console.log('\n🎉 Testes concluídos!');
    
  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

main();
