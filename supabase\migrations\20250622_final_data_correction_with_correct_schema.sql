DO $$
DECLARE
  v_guardian_user_id UUID;
  v_student_franklin_id UUID;
  v_student_sarah_id UUID;
  v_student_mauricio_id UUID;
BEGIN
  -- PASSO 1: Corrigir o tipo de usuário do guardião para 'guardian'.
  RAISE NOTICE 'Updating guardian profile user_type from parent to guardian...';
  UPDATE public.profiles
  SET user_type = 'guardian'
  WHERE email = '<EMAIL>' AND user_type = 'parent';
  RAISE NOTICE 'Guardian user_type updated.';

  -- PASSO 2: Obter os IDs de usuário (UUIDs) usando os e-mails CORRETOS.
  RAISE NOTICE 'Fetching user IDs based on the correct emails...';
  SELECT user_id INTO v_guardian_user_id FROM public.profiles WHERE email = '<EMAIL>';
  SELECT user_id INTO v_student_franklin_id FROM public.profiles WHERE email = '<EMAIL>';
  SELECT user_id INTO v_student_sarah_id FROM public.profiles WHERE email = '<EMAIL>';
  SELECT user_id INTO v_student_mauricio_id FROM public.profiles WHERE email = '<EMAIL>';

  -- PASSO 3: Validar que todos os perfis foram encontrados.
  IF v_guardian_user_id IS NULL OR v_student_franklin_id IS NULL OR v_student_sarah_id IS NULL OR v_student_mauricio_id IS NULL THEN
    RAISE EXCEPTION 'CRITICAL: One or more user profiles were not found. Aborting.';
  END IF;
  RAISE NOTICE 'All user IDs fetched successfully.';

  -- PASSO 4: Limpar quaisquer associações antigas para estes estudantes.
  RAISE NOTICE 'Deleting old guardian relationships...';
  DELETE FROM public.guardians
  WHERE student_id IN (v_student_franklin_id, v_student_sarah_id, v_student_mauricio_id);
  RAISE NOTICE 'Old relationships cleared.';

  -- PASSO 5: Inserir os vínculos corretos na tabela 'guardians' usando a coluna correta 'guardian_profile_id'.
  RAISE NOTICE 'Inserting new relationships using the correct column name guardian_profile_id...';
  INSERT INTO public.guardians (student_id, guardian_profile_id, is_active)
  VALUES
    (v_student_franklin_id, v_guardian_user_id, true),
    (v_student_sarah_id, v_guardian_user_id, true),
    (v_student_mauricio_id, v_guardian_user_id, true);
  RAISE NOTICE 'Inserted 3 new rows into guardians table.';

  RAISE NOTICE 'SUCCESS: Data correction is complete.';

END;
$$; 