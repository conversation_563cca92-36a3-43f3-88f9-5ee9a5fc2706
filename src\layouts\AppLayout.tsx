import { Outlet, useNavigate, Link } from 'react-router-dom';
import { useUser } from '@/contexts/UnifiedAuthContext';
import { useToast } from '@/components/ui/use-toast';
import { useAuthRedirect } from '@/hooks/useAuthRedirect';
import { OfflineStatusBanner } from '@/components/offline/OfflineStatusBanner';
import { NetworkStatusIndicator } from '@/components/offline/NetworkStatusIndicator';
import { SmartCacheIndicator } from '@/components/offline/SmartCacheIndicator';
import { SyncStatusIndicator } from '@/components/offline/SyncStatusIndicator';
import ApiErrorBanner from '@/components/ApiErrorBanner';
import { Button } from '@/components/ui/button';
import { Bug, Home, MapPin, Zap } from 'lucide-react';

export default function AppLayout() {
  const { user, loading } = useUser();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Handle redirect if user is not authenticated
  useAuthRedirect(!!user, loading, navigate, toast);

  // Show loading spinner while checking auth
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-blue-600 rounded-full border-t-transparent"></div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-gray-50">
      {/* API Error Banner for service unavailable errors */}
      <ApiErrorBanner />
      
      {/* Banner de status offline */}
      <OfflineStatusBanner />
      
      {/* Header with navigation and status indicators */}
      <header className="bg-white shadow-sm border-b sticky top-0 z-40">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-6">
              <h1 className="text-xl font-semibold text-gray-900">
                EduConnect
              </h1>

              {/* Quick Navigation */}
              <nav className="hidden md:flex items-center gap-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link to={user?.user_metadata?.user_type === 'parent' ? '/parent-dashboard' : '/student-dashboard'}>
                    <Home className="h-4 w-4 mr-1" />
                    Dashboard
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/student-map">
                    <MapPin className="h-4 w-4 mr-1" />
                    Mapa
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/ai-completion">
                    <Zap className="h-4 w-4 mr-1" />
                    AI
                  </Link>
                </Button>
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/debug-parent-dashboard">
                    <Bug className="h-4 w-4 mr-1" />
                    Debug
                  </Link>
                </Button>
              </nav>
            </div>

            <div className="flex items-center gap-4">
              <NetworkStatusIndicator />
              <SmartCacheIndicator showDetails />
              <SyncStatusIndicator />

              <div className="text-sm text-gray-600">
                {user?.user_metadata?.full_name || user?.email}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="container mx-auto px-4 py-6">
        <Outlet />
      </main>
    </div>
  );
}
