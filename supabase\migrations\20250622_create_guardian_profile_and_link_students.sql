DO $$
DECLARE
  v_guardian_user_id UUID;
  v_guardian_full_name TEXT;
  v_guardian_email TEXT;
  
  v_student_franklin_id UUID;
  v_student_sarah_id UUID;
  v_student_mauricio_id UUID;
BEGIN
  -- PASSO 1: Obter os dados do guardião da tabela principal de perfis.
  RAISE NOTICE 'Fetching guardian data from public.profiles...';
  SELECT user_id, full_name, email 
  INTO v_guardian_user_id, v_guardian_full_name, v_guardian_email
  FROM public.profiles 
  WHERE email = '<EMAIL>';

  IF v_guardian_user_id IS NULL THEN
    RAISE EXCEPTION 'Guardian <NAME_EMAIL> not found in public.profiles. Aborting.';
  END IF;
  RAISE NOTICE 'Guardian data fetched successfully: ID %', v_guardian_user_id;

  -- PASSO 2: Inserir o registro do guardião na tabela 'guardian_profiles' SE ele ainda não existir.
  -- Este é o passo CRÍTICO que estava faltando e causava o erro da chave estrangeira.
  RAISE NOTICE 'Ensuring guardian exists in guardian_profiles table...';
  INSERT INTO public.guardian_profiles (id, user_id, full_name, email, status)
  VALUES (v_guardian_user_id, v_guardian_user_id, v_guardian_full_name, v_guardian_email, 'active')
  ON CONFLICT (id) DO NOTHING;
  RAISE NOTICE 'Guardian profile ensured in guardian_profiles.';

  -- PASSO 3: Obter os IDs dos estudantes.
  RAISE NOTICE 'Fetching student IDs...';
  SELECT user_id INTO v_student_franklin_id FROM public.profiles WHERE email = '<EMAIL>';
  SELECT user_id INTO v_student_sarah_id FROM public.profiles WHERE email = '<EMAIL>';
  SELECT user_id INTO v_student_mauricio_id FROM public.profiles WHERE email = '<EMAIL>';

  IF v_student_franklin_id IS NULL OR v_student_sarah_id IS NULL OR v_student_mauricio_id IS NULL THEN
    RAISE EXCEPTION 'One or more student profiles could not be found. Aborting.';
  END IF;
  RAISE NOTICE 'Student IDs fetched successfully.';

  -- PASSO 4: Limpar quaisquer vínculos antigos para estes estudantes.
  RAISE NOTICE 'Deleting old relationships...';
  DELETE FROM public.guardians WHERE student_id IN (v_student_franklin_id, v_student_sarah_id, v_student_mauricio_id);
  RAISE NOTICE 'Old relationships cleared.';

  -- PASSO 5: Inserir os vínculos corretos usando a coluna 'guardian_profile_id'.
  RAISE NOTICE 'Inserting new, correct relationships...';
  INSERT INTO public.guardians (student_id, guardian_profile_id, is_active)
  VALUES
    (v_student_franklin_id, v_guardian_user_id, true),
    (v_student_sarah_id, v_guardian_user_id, true),
    (v_student_mauricio_id, v_guardian_user_id, true);
  RAISE NOTICE 'Inserted 3 new relationship rows.';

  RAISE NOTICE 'SUCCESS: Migration complete. The guardian profile has been created and students are correctly linked.';
END;
$$; 