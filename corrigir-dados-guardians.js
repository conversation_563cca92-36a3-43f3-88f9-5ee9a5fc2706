import { Client } from 'pg';

// Configurações de conexão
const config = {
  host: 'aws-0-eu-west-2.pooler.supabase.com',
  port: 6543,
  database: 'postgres',
  user: 'postgres.rsvjnndhbyyxktbczlnk',
  password: 'P+-@@6CUDUJSUpy',
  ssl: {
    rejectUnauthorized: false
  }
};

async function corrigirDadosGuardians() {
  const client = new Client(config);
  
  try {
    console.log('🔌 Conectando ao banco de dados...');
    await client.connect();
    console.log('✅ Conexão estabelecida!\n');

    // 1. Verificar dados atuais
    console.log('🔍 VERIFICANDO DADOS ATUAIS:');
    console.log('=====================================');
    
    const guardiansAtual = await client.query(`
      SELECT g.id, g.student_id, g.email, g.full_name, g.is_active,
             p.full_name as student_name, p.email as student_email
      FROM public.guardians g
      LEFT JOIN public.profiles p ON g.student_id = p.user_id
      ORDER BY g.created_at DESC;
    `);
    
    console.log(`📊 Total de relacionamentos atuais: ${guardiansAtual.rows.length}`);
    guardiansAtual.rows.forEach((guardian, index) => {
      console.log(`${index + 1}. Guardian ID: ${guardian.id}`);
      console.log(`   Student ID: ${guardian.student_id}`);
      console.log(`   Guardian Email: ${guardian.email || 'NULL'}`);
      console.log(`   Guardian Name: ${guardian.full_name || 'NULL'}`);
      console.log(`   Student: ${guardian.student_name || 'N/A'} (${guardian.student_email || 'N/A'})`);
      console.log('');
    });

    // 2. Identificar o usuário responsável (Mauro Frank Lima de Lima)
    const responsavelEmail = '<EMAIL>';
    const responsavelNome = 'Mauro Frank Lima de Lima';
    
    console.log(`👤 RESPONSÁVEL ALVO: ${responsavelNome} (${responsavelEmail})`);
    console.log('=====================================');

    // 3. Identificar estudantes que precisam ser vinculados
    const estudantes = await client.query(`
      SELECT user_id, full_name, email
      FROM public.profiles
      WHERE user_type = 'student'
      AND user_id IS NOT NULL
      ORDER BY created_at;
    `);
    
    console.log(`👥 ESTUDANTES DISPONÍVEIS: ${estudantes.rows.length}`);
    estudantes.rows.forEach((estudante, index) => {
      console.log(`${index + 1}. ${estudante.full_name} (${estudante.email})`);
      console.log(`   User ID: ${estudante.user_id}`);
    });
    console.log('');

    // 4. Limpar dados inconsistentes
    console.log('🧹 LIMPANDO DADOS INCONSISTENTES:');
    console.log('=====================================');
    
    // Remover registros com dados NULL ou inconsistentes
    const deleteResult = await client.query(`
      DELETE FROM public.guardians 
      WHERE email IS NULL 
         OR full_name IS NULL 
         OR student_id IS NULL;
    `);
    
    console.log(`🗑️ Removidos ${deleteResult.rowCount} registros inconsistentes`);

    // 5. Criar relacionamentos corretos
    console.log('\n✨ CRIANDO RELACIONAMENTOS CORRETOS:');
    console.log('=====================================');
    
    let relacionamentosCriados = 0;
    
    for (const estudante of estudantes.rows) {
      try {
        // Verificar se já existe relacionamento
        const existeRelacionamento = await client.query(`
          SELECT id FROM public.guardians 
          WHERE student_id = $1 AND email = $2;
        `, [estudante.user_id, responsavelEmail]);
        
        if (existeRelacionamento.rows.length === 0) {
          // Criar novo relacionamento
          const insertResult = await client.query(`
            INSERT INTO public.guardians (
              student_id, 
              email, 
              full_name, 
              is_active, 
              created_at, 
              updated_at
            )
            VALUES ($1, $2, $3, $4, NOW(), NOW())
            RETURNING id;
          `, [
            estudante.user_id,
            responsavelEmail,
            responsavelNome,
            true
          ]);
          
          console.log(`✅ Criado relacionamento: ${responsavelNome} -> ${estudante.full_name}`);
          console.log(`   Guardian ID: ${insertResult.rows[0].id}`);
          console.log(`   Student ID: ${estudante.user_id}`);
          relacionamentosCriados++;
        } else {
          console.log(`⚠️ Relacionamento já existe: ${responsavelNome} -> ${estudante.full_name}`);
        }
      } catch (error) {
        console.error(`❌ Erro ao criar relacionamento para ${estudante.full_name}: ${error.message}`);
      }
    }

    // 6. Criar registros na tabela students
    console.log('\n🎓 CRIANDO REGISTROS NA TABELA STUDENTS:');
    console.log('=====================================');
    
    // Buscar o user_id do responsável
    const responsavelResult = await client.query(`
      SELECT user_id FROM public.profiles 
      WHERE email = $1;
    `, [responsavelEmail]);
    
    if (responsavelResult.rows.length > 0) {
      const responsavelUserId = responsavelResult.rows[0].user_id;
      console.log(`👤 Responsável User ID: ${responsavelUserId}`);
      
      let estudantesCriados = 0;
      
      for (const estudante of estudantes.rows) {
        try {
          // Verificar se já existe na tabela students
          const existeEstudante = await client.query(`
            SELECT id FROM public.students WHERE id = $1;
          `, [estudante.user_id]);
          
          if (existeEstudante.rows.length === 0) {
            // Criar registro do estudante
            await client.query(`
              INSERT INTO public.students (
                id,
                name,
                school_id,
                school_name,
                grade,
                class,
                guardian_id,
                location_sharing,
                created_at,
                updated_at
              )
              VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW());
            `, [
              estudante.user_id,
              estudante.full_name,
              'ESC001',
              'Escola Municipal Exemplo',
              '9º Ano',
              'A',
              responsavelUserId,
              true
            ]);
            
            console.log(`✅ Criado registro de estudante: ${estudante.full_name}`);
            estudantesCriados++;
          } else {
            console.log(`⚠️ Estudante já existe na tabela students: ${estudante.full_name}`);
          }
        } catch (error) {
          console.error(`❌ Erro ao criar estudante ${estudante.full_name}: ${error.message}`);
        }
      }
      
      console.log(`📊 Total de estudantes criados: ${estudantesCriados}`);
    } else {
      console.log(`❌ Responsável não encontrado: ${responsavelEmail}`);
    }

    // 7. Verificar resultado final
    console.log('\n🎯 VERIFICAÇÃO FINAL:');
    console.log('=====================================');
    
    const resultadoFinal = await client.query(`
      SELECT g.id, g.student_id, g.email, g.full_name, g.is_active,
             p.full_name as student_name, p.email as student_email
      FROM public.guardians g
      LEFT JOIN public.profiles p ON g.student_id = p.user_id
      WHERE g.email = $1
      ORDER BY g.created_at DESC;
    `, [responsavelEmail]);
    
    console.log(`✅ Total de relacionamentos para ${responsavelEmail}: ${resultadoFinal.rows.length}`);
    resultadoFinal.rows.forEach((guardian, index) => {
      console.log(`${index + 1}. ${guardian.full_name} -> ${guardian.student_name}`);
      console.log(`   Guardian ID: ${guardian.id}`);
      console.log(`   Student ID: ${guardian.student_id}`);
      console.log(`   Email: ${guardian.email}`);
      console.log('');
    });

    // 8. Testar a função RPC
    console.log('🧪 TESTANDO FUNÇÃO RPC:');
    console.log('=====================================');
    
    try {
      // Simular a função RPC manualmente
      const rpcResult = await client.query(`
        SELECT
          u.id as student_id,
          u.email as student_email,
          COALESCE(p.full_name, '') as student_name
        FROM
          public.guardians g
          JOIN auth.users u ON g.student_id = u.id
          LEFT JOIN public.profiles p ON p.user_id = u.id
        WHERE
          g.email = $1
          AND g.is_active = true;
      `, [responsavelEmail]);
      
      console.log(`📊 Função RPC retornaria ${rpcResult.rows.length} estudantes:`);
      rpcResult.rows.forEach((student, index) => {
        console.log(`${index + 1}. ${student.student_name} (${student.student_email})`);
        console.log(`   Student ID: ${student.student_id}`);
      });
    } catch (error) {
      console.error(`❌ Erro ao testar função RPC: ${error.message}`);
    }

    console.log('\n🎉 CORREÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('=====================================');
    console.log(`✅ Relacionamentos criados: ${relacionamentosCriados}`);
    console.log(`✅ Dados inconsistentes removidos: ${deleteResult.rowCount}`);
    console.log('✅ Agora o Parent Dashboard deve mostrar os estudantes corretamente!');
    console.log('\n🔄 Próximos passos:');
    console.log('1. Recarregue o Parent Dashboard');
    console.log('2. Verifique se os estudantes aparecem');
    console.log('3. Use a página de debug se necessário');

  } catch (error) {
    console.error('❌ Erro na correção:', error.message);
  } finally {
    await client.end();
  }
}

// Executar a correção
corrigirDadosGuardians();
