/**
 * AI Completion Settings Component
 * Provides UI for configuring AI completion preferences
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/switch';
import { Slider } from '../ui/slider';

import { Badge } from '../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Alert, AlertDescription } from '../ui/alert';
import { Separator } from '../ui/separator';
import {
  aiCompletionConfig,
  AICompletionConfig
} from '../../lib/config/ai-completion-config';
import { APIKeyTester } from './APIKeyTester';
import { Eye, EyeOff, Download, Upload, RotateCcw, Save, AlertTriangle, Loader2 } from 'lucide-react';

export function AICompletionSettings() {
  const [config, setConfig] = useState<AICompletionConfig>(aiCompletionConfig.getConfig());
  const [apiKey, setApiKey] = useState('');
  const [showApiKey, setShowApiKey] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [apiKeyError, setApiKeyError] = useState<string | null>(null);
  const [isValidatingApiKey, setIsValidatingApiKey] = useState(false);
  const [newFileType, setNewFileType] = useState('');

  // Load current configuration
  useEffect(() => {
    const currentConfig = aiCompletionConfig.getConfig();
    setConfig(currentConfig);
    setApiKey(aiCompletionConfig.getApiKey());
  }, []);

  // Subscribe to configuration changes
  useEffect(() => {
    const unsubscribe = aiCompletionConfig.subscribe((newConfig) => {
      setConfig(newConfig);
      setHasChanges(false);
    });
    return unsubscribe;
  }, []);

  const handleConfigChange = (updates: Partial<AICompletionConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    setHasChanges(true);
  };

  const handleSave = async () => {
    setSaveStatus('saving');
    setApiKeyError(null);

    try {
      // Validate configuration
      const errors = aiCompletionConfig.validateConfig(config);
      if (errors.length > 0) {
        throw new Error(errors.join(', '));
      }

      // Update API key if changed
      if (apiKey !== aiCompletionConfig.getApiKey()) {
        setIsValidatingApiKey(true);
        const result = await aiCompletionConfig.setApiKey(apiKey);
        setIsValidatingApiKey(false);

        if (!result.success) {
          setApiKeyError(result.error || 'Failed to validate API key');
          setSaveStatus('error');
          setTimeout(() => setSaveStatus('idle'), 3000);
          return;
        }
      }

      // Update configuration
      aiCompletionConfig.updateConfig(config);

      setSaveStatus('saved');
      setHasChanges(false);

      setTimeout(() => setSaveStatus('idle'), 2000);
    } catch (error) {
      console.error('Failed to save configuration:', error);
      setSaveStatus('error');
      setApiKeyError(error instanceof Error ? error.message : 'Unknown error');
      setTimeout(() => setSaveStatus('idle'), 3000);
    }
  };

  const handleReset = () => {
    aiCompletionConfig.resetToDefaults();
    setApiKey('');
    aiCompletionConfig.clearApiKey();
    setHasChanges(false);
  };

  const handleExport = () => {
    try {
      const configJson = aiCompletionConfig.exportConfig();
      const blob = new Blob([configJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'ai-completion-config.json';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to export configuration:', error);
    }
  };

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const configJson = e.target?.result as string;
        aiCompletionConfig.importConfig(configJson);
        setHasChanges(false);
      } catch (error) {
        console.error('Failed to import configuration:', error);
        alert(`Failed to import configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    };
    reader.readAsText(file);
  };

  const addFileType = () => {
    if (newFileType.trim() && !config.enabledFileTypes.includes(newFileType.trim())) {
      handleConfigChange({
        enabledFileTypes: [...config.enabledFileTypes, newFileType.trim()]
      });
      setNewFileType('');
    }
  };

  const removeFileType = (fileType: string, list: 'enabled' | 'disabled') => {
    if (list === 'enabled') {
      handleConfigChange({
        enabledFileTypes: config.enabledFileTypes.filter(type => type !== fileType)
      });
    } else {
      handleConfigChange({
        disabledFileTypes: config.disabledFileTypes.filter(type => type !== fileType)
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">AI Code Completion Settings</h2>
          <p className="text-muted-foreground">
            Configure your AI-powered code completion preferences
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            disabled={saveStatus === 'saving'}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={handleSave}
            disabled={!hasChanges || saveStatus === 'saving'}
            className={saveStatus === 'saved' ? 'bg-green-600' : ''}
          >
            <Save className="h-4 w-4 mr-2" />
            {saveStatus === 'saving' ? 'Saving...' : saveStatus === 'saved' ? 'Saved!' : 'Save'}
          </Button>
        </div>
      </div>

      {saveStatus === 'error' && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {apiKeyError || 'Failed to save configuration. Please check your settings and try again.'}
          </AlertDescription>
        </Alert>
      )}

      {apiKeyError && saveStatus !== 'error' && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>API Key Error:</strong> {apiKeyError}
            <br />
            <span className="text-sm mt-1 block">
              💡 <strong>Tip:</strong> Make sure you're using an individual API key from{' '}
              <a href="https://codeium.com" target="_blank" rel="noopener noreferrer" className="underline">
                codeium.com
              </a>{' '}
              (not a team account key).
            </span>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList>
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="apikey">API Key Setup</TabsTrigger>
          <TabsTrigger value="behavior">Behavior</TabsTrigger>
          <TabsTrigger value="filetypes">File Types</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>API Configuration</CardTitle>
              <CardDescription>
                Configure your AI completion service connection
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="apiKey">API Key</Label>
                <div className="flex gap-2">
                  <Input
                    id="apiKey"
                    type={showApiKey ? 'text' : 'password'}
                    value={apiKey}
                    onChange={(e) => {
                      setApiKey(e.target.value);
                      setApiKeyError(null); // Clear error when user types
                    }}
                    placeholder="Enter your individual API key from codeium.com"
                    className="flex-1"
                    disabled={isValidatingApiKey}
                  />
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowApiKey(!showApiKey)}
                    disabled={isValidatingApiKey}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
                {isValidatingApiKey && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Validating API key...
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="baseUrl">Base URL</Label>
                <Input
                  id="baseUrl"
                  value={config.baseUrl}
                  onChange={(e) => handleConfigChange({ baseUrl: e.target.value })}
                  placeholder="https://server.codeium.com"
                />
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="enabled"
                  checked={config.enabled}
                  onCheckedChange={(enabled) => handleConfigChange({ enabled })}
                />
                <Label htmlFor="enabled">Enable AI Code Completion</Label>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="apikey" className="space-y-4">
          <APIKeyTester />
        </TabsContent>

        <TabsContent value="behavior" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Completion Behavior</CardTitle>
              <CardDescription>
                Customize how completions are triggered and displayed
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Debounce Delay: {config.debounceMs}ms</Label>
                <Slider
                  value={[config.debounceMs]}
                  onValueChange={([debounceMs]) => handleConfigChange({ debounceMs })}
                  min={0}
                  max={1000}
                  step={50}
                  className="w-full"
                />
                <p className="text-sm text-muted-foreground">
                  How long to wait after typing before requesting completions
                </p>
              </div>

              <div className="space-y-2">
                <Label>Max Suggestions: {config.maxSuggestions}</Label>
                <Slider
                  value={[config.maxSuggestions]}
                  onValueChange={([maxSuggestions]) => handleConfigChange({ maxSuggestions })}
                  min={1}
                  max={10}
                  step={1}
                  className="w-full"
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="autoTrigger"
                    checked={config.autoTrigger}
                    onCheckedChange={(autoTrigger) => handleConfigChange({ autoTrigger })}
                  />
                  <Label htmlFor="autoTrigger">Auto-trigger completions</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="showInlinePreview"
                    checked={config.showInlinePreview}
                    onCheckedChange={(showInlinePreview) => handleConfigChange({ showInlinePreview })}
                  />
                  <Label htmlFor="showInlinePreview">Show inline preview</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="acceptOnTab"
                    checked={config.acceptOnTab}
                    onCheckedChange={(acceptOnTab) => handleConfigChange({ acceptOnTab })}
                  />
                  <Label htmlFor="acceptOnTab">Accept completion with Tab</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="acceptOnEnter"
                    checked={config.acceptOnEnter}
                    onCheckedChange={(acceptOnEnter) => handleConfigChange({ acceptOnEnter })}
                  />
                  <Label htmlFor="acceptOnEnter">Accept completion with Enter</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="filetypes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>File Type Configuration</CardTitle>
              <CardDescription>
                Control which file types have AI completion enabled
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>Enabled File Types</Label>
                <div className="flex flex-wrap gap-2">
                  {config.enabledFileTypes.map((fileType) => (
                    <Badge
                      key={fileType}
                      variant="default"
                      className="cursor-pointer"
                      onClick={() => removeFileType(fileType, 'enabled')}
                    >
                      {fileType} ×
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Input
                  value={newFileType}
                  onChange={(e) => setNewFileType(e.target.value)}
                  placeholder="Add file type (e.g., python, java)"
                  onKeyPress={(e) => e.key === 'Enter' && addFileType()}
                />
                <Button onClick={addFileType} disabled={!newFileType.trim()}>
                  Add
                </Button>
              </div>

              <div className="space-y-2">
                <Label>Disabled File Types</Label>
                <div className="flex flex-wrap gap-2">
                  {config.disabledFileTypes.map((fileType) => (
                    <Badge
                      key={fileType}
                      variant="secondary"
                      className="cursor-pointer"
                      onClick={() => removeFileType(fileType, 'disabled')}
                    >
                      {fileType} ×
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Settings</CardTitle>
              <CardDescription>
                Advanced configuration and data management
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="telemetryEnabled"
                  checked={config.telemetryEnabled}
                  onCheckedChange={(telemetryEnabled) => handleConfigChange({ telemetryEnabled })}
                />
                <Label htmlFor="telemetryEnabled">Enable telemetry</Label>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label>Configuration Management</Label>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleExport}>
                    <Download className="h-4 w-4 mr-2" />
                    Export Config
                  </Button>
                  <div>
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleImport}
                      style={{ display: 'none' }}
                      id="import-config"
                    />
                    <Button
                      variant="outline"
                      onClick={() => document.getElementById('import-config')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Import Config
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
