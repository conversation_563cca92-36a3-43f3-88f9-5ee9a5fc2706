/**
 * API Key Testing Component
 * Helps users test and validate their Codeium API keys
 */

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { AICompletionService } from '../../lib/services/ai-completion';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  AlertTriangle, 
  ExternalLink,
  Key,
  Info
} from 'lucide-react';

export function APIKeyTester() {
  const [testApiKey, setTestApiKey] = useState('');
  const [isTestingKey, setIsTestingKey] = useState(false);
  const [testResult, setTestResult] = useState<{
    status: 'idle' | 'success' | 'error';
    message?: string;
  }>({ status: 'idle' });

  const handleTestApiKey = async () => {
    if (!testApiKey.trim()) {
      setTestResult({
        status: 'error',
        message: 'Please enter an API key to test'
      });
      return;
    }

    setIsTestingKey(true);
    setTestResult({ status: 'idle' });

    try {
      const service = new AICompletionService(testApiKey.trim());
      const validation = await service.validateApiKey();

      if (validation.valid) {
        setTestResult({
          status: 'success',
          message: 'API key is valid and working! You can now use this key in your settings.'
        });
      } else {
        setTestResult({
          status: 'error',
          message: validation.error || 'API key validation failed'
        });
      }
    } catch (error) {
      setTestResult({
        status: 'error',
        message: `Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsTestingKey(false);
    }
  };

  const getStatusIcon = () => {
    switch (testResult.status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Key className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = () => {
    switch (testResult.status) {
      case 'success':
        return <Badge variant="default" className="bg-green-600">Valid</Badge>;
      case 'error':
        return <Badge variant="destructive">Invalid</Badge>;
      default:
        return <Badge variant="secondary">Not Tested</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Key className="h-5 w-5" />
          API Key Tester
        </CardTitle>
        <CardDescription>
          Test your Codeium API key before saving it to your settings
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Instructions */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Getting your API key:</strong>
            <ol className="list-decimal list-inside mt-2 space-y-1 text-sm">
              <li>Go to <a href="https://codeium.com" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline inline-flex items-center gap-1">codeium.com <ExternalLink className="h-3 w-3" /></a></li>
              <li>Sign up for an <strong>individual account</strong> (not team)</li>
              <li>Navigate to your account settings</li>
              <li>Generate a new API key</li>
              <li>Copy and test it here</li>
            </ol>
          </AlertDescription>
        </Alert>

        {/* Test Input */}
        <div className="space-y-2">
          <Label htmlFor="testApiKey">Test API Key</Label>
          <div className="flex gap-2">
            <Input
              id="testApiKey"
              type="password"
              value={testApiKey}
              onChange={(e) => {
                setTestApiKey(e.target.value);
                setTestResult({ status: 'idle' });
              }}
              placeholder="Paste your API key here to test"
              className="flex-1"
              disabled={isTestingKey}
            />
            <Button
              onClick={handleTestApiKey}
              disabled={isTestingKey || !testApiKey.trim()}
              className="min-w-[100px]"
            >
              {isTestingKey ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                'Test Key'
              )}
            </Button>
          </div>
        </div>

        {/* Test Result */}
        {testResult.status !== 'idle' && (
          <Alert variant={testResult.status === 'error' ? 'destructive' : 'default'}>
            <div className="flex items-start gap-2">
              {getStatusIcon()}
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-medium">Test Result:</span>
                  {getStatusBadge()}
                </div>
                <AlertDescription>
                  {testResult.message}
                </AlertDescription>
              </div>
            </div>
          </Alert>
        )}

        {/* Common Issues */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Common Issues:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
              <li><strong>Team subscription not active:</strong> You're using a team API key. Get an individual key instead.</li>
              <li><strong>Invalid API key:</strong> Check that you copied the complete key without extra spaces.</li>
              <li><strong>Network error:</strong> Check your internet connection and try again.</li>
              <li><strong>Rate limiting:</strong> Wait a few minutes and try again.</li>
            </ul>
          </AlertDescription>
        </Alert>

        {/* Success Actions */}
        {testResult.status === 'success' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-green-800 font-medium mb-2">
              <CheckCircle className="h-4 w-4" />
              API Key Validated Successfully!
            </div>
            <p className="text-green-700 text-sm mb-3">
              Your API key is working correctly. You can now copy it to your AI Completion settings.
            </p>
            <Button
              size="sm"
              onClick={() => {
                navigator.clipboard.writeText(testApiKey);
                // You could add a toast notification here
              }}
              className="bg-green-600 hover:bg-green-700"
            >
              Copy Key to Clipboard
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
