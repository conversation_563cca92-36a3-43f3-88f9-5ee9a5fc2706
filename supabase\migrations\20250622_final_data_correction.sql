-- Passo 1: Atualizar o tipo de usuário do guardião para garantir que ele seja reconhecido como tal.
-- <PERSON><PERSON> corrige a violação de chave estrangeira onde o perfil do guardião não era encontrado na 'view' de guardiões.
UPDATE public.profiles
SET user_type = 'guardian'
WHERE email = '<EMAIL>' AND user_type IS DISTINCT FROM 'guardian';

-- Passo 2: Declarar variáveis para armazenar os IDs.
DO $$
DECLARE
  v_guardian_id UUID;
  v_student_franklin UUID;
  v_student_sarah UUID;
  v_student_mauricio UUID;
BEGIN
  -- Passo 3: Obter o ID do guardião (agora garantido que ele tem o tipo de usuário correto).
  SELECT user_id INTO v_guardian_id FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1;
  RAISE NOTICE 'Guardian <NAME_EMAIL>: %', v_guardian_id;

  -- Obter IDs dos estudantes
  SELECT user_id INTO v_student_franklin FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1;
  RAISE NOTICE 'Student <NAME_EMAIL>: %', v_student_franklin;

  SELECT user_id INTO v_student_sarah FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1;
  RAISE NOTICE 'Student <NAME_EMAIL>: %', v_student_sarah;

  SELECT user_id INTO v_student_mauricio FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1;
  RAISE NOTICE 'Student <NAME_EMAIL>: %', v_student_mauricio;

  -- Verificar se todos os IDs foram encontrados
  IF v_guardian_id IS NULL OR v_student_franklin IS NULL OR v_student_sarah IS NULL OR v_student_mauricio IS NULL THEN
    RAISE EXCEPTION 'Um ou mais perfis (guardião ou estudantes) não foram encontrados. Abortando.';
  END IF;

  -- Passo 4: Remover quaisquer associações antigas e incorretas para estes estudantes para evitar duplicatas.
  RAISE NOTICE 'Deleting old guardian relationships for the students...';
  DELETE FROM public.guardians
  WHERE student_id IN (v_student_franklin, v_student_sarah, v_student_mauricio);
  RAISE NOTICE 'Deleted % rows from guardians table.', (SELECT count(*) FROM pg_stat_activity);


  -- Passo 5: Inserir as novas e corretas associações.
  -- A coluna de referência em 'guardians' é 'guardian_id', que agora corresponde ao 'user_id' do perfil do guardião.
  RAISE NOTICE 'Inserting new correct relationships...';
  INSERT INTO public.guardians (student_id, guardian_id, is_active)
  VALUES
    (v_student_franklin, v_guardian_id, true),
    (v_student_sarah, v_guardian_id, true),
    (v_student_mauricio, v_guardian_id, true);
  RAISE NOTICE 'Inserted 3 correct relationship rows.';

  RAISE NOTICE 'Data correction complete. The students are now correctly linked to the guardian.';

END;
$$; 