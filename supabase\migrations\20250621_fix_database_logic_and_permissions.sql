-- FINAL MIGRATION: Fix Database Logic and Permissions
-- Date: 2025-06-21
-- This script corrects all identified RLS policy errors and fixes the get_guardian_students function.
-- This is the only migration script that needs to be run to solve the current issues.

-- =================================================================
-- Part 1: Correct Row Level Security (RLS) Policies
-- =================================================================

-- Fix Policy for 'profiles' table
-- The root cause of "uuid = integer" error was comparing auth.uid() to the 'id' (integer) column.
-- This policy correctly compares auth.uid() to the 'user_id' (uuid) column.
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Authenticated users can view their own profile" ON public.profiles;
CREATE POLICY "Authenticated users can view their own profile"
ON public.profiles FOR SELECT
TO authenticated
USING (auth.uid() = user_id);
COMMENT ON POLICY "Authenticated users can view their own profile" ON public.profiles IS 'Users can only view their own profile. Fixes uuid/integer mismatch.';

-- Fix Policy for 'guardians' table
-- Ensures guardians can only see the student relationships they are part of.
ALTER TABLE public.guardians ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Guardians can view their own guardian entries" ON public.guardians;
CREATE POLICY "Guardians can view their own guardian entries"
ON public.guardians FOR SELECT
TO authenticated
USING (auth.uid() = guardian_profile_id);
COMMENT ON POLICY "Guardians can view their own guardian entries" ON public.guardians IS 'Guardians can only view their own student links.';

-- (Safety) Add Policy for 'locations' table
-- Ensures users can only see their own location entries.
ALTER TABLE public.locations ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view their own locations" ON public.locations;
CREATE POLICY "Users can view their own locations"
ON public.locations FOR SELECT
TO authenticated
USING (auth.uid() = user_id);
COMMENT ON POLICY "Users can view their own locations" ON public.locations IS 'Users can only view their own location records.';


-- =================================================================
-- Part 2: Correct the 'get_guardian_students' RPC Function
-- =================================================================

-- This function now uses the correct table joins and logic based on the verified schema.
DROP FUNCTION IF EXISTS public.get_guardian_students();
CREATE OR REPLACE FUNCTION public.get_guardian_students()
RETURNS TABLE (
  student_id UUID,
  student_email TEXT,
  student_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER -- Important for RLS to work inside the function
STABLE
AS $$
DECLARE
  current_guardian_id UUID;
BEGIN
  -- Get the UUID of the currently authenticated user.
  current_guardian_id := auth.uid();

  -- Raise a notice for debugging purposes.
  -- RAISE NOTICE 'Fetching students for guardian_id: %', current_guardian_id;

  -- Return the list of students associated with the guardian.
  -- JOIN guardians with profiles on the correct UUID columns.
  RETURN QUERY
    SELECT DISTINCT
        p.user_id AS student_id,
        p.email AS student_email,
        p.full_name AS student_name
    FROM
        public.guardians AS g
    JOIN
        public.profiles AS p ON g.student_id = p.user_id
    WHERE
        g.guardian_profile_id = current_guardian_id
        AND g.is_active = TRUE; -- Optional: only return active relationships
END;
$$;

-- Grant execution rights to authenticated users
GRANT EXECUTE ON FUNCTION public.get_guardian_students() TO authenticated;

COMMENT ON FUNCTION public.get_guardian_students() IS 'Final Version: Returns a DISTINCT list of students linked to the authenticated guardian, using correct table joins and types.'; 