
import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Database, CheckCircle, XCircle } from 'lucide-react';

interface DiagnosticResult {
  category: string;
  status: 'success' | 'error' | 'warning';
  message: string;
  details?: any;
}

const DiagnosticTool: React.FC = () => {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { toast } = useToast();

  const runDiagnostics = async () => {
    setIsRunning(true);
    setResults([]);
    const newResults: DiagnosticResult[] = [];

    try {
      // Test 1: Database Connection
      try {
        const { error } = await supabase
          .from('profiles')
          .select('id')
          .limit(1);
        if (error) throw error;
        
        newResults.push({
          category: 'Database',
          status: 'success',
          message: 'Conexão com o banco de dados estabelecida com sucesso'
        });
      } catch (error: any) {
        newResults.push({
          category: 'Database',
          status: 'error',
          message: `Erro na conexão com o banco: ${error.message}`
        });
      }

      // Test 2: Auth Service
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        newResults.push({
          category: 'Authentication',
          status: session ? 'success' : 'warning',
          message: session ? 'Usuário autenticado' : 'Nenhum usuário autenticado',
          details: session?.user ? {
            userId: session.user.id,
            email: session.user.email,
            userType: session.user.user_metadata?.user_type
          } : null
        });
      } catch (error: any) {
        newResults.push({
          category: 'Authentication',
          status: 'error',
          message: `Erro no serviço de autenticação: ${error.message}`
        });
      }

      // Test 3: User Profiles Table
      try {
        const { data: profiles, error } = await supabase
          .from('profiles')
          .select('user_type, id')
          .limit(5);
          
        if (error) throw error;
        
        newResults.push({
          category: 'User Profiles',
          status: 'success',
          message: `Tabela de perfis acessível (${profiles?.length || 0} registros encontrados)`,
          details: profiles
        });
      } catch (error: any) {
        newResults.push({
          category: 'User Profiles',
          status: 'error',
          message: `Erro ao acessar tabela de perfis: ${error.message}`
        });
      }

      // Test 4: Guardians Table
      try {
        const { error } = await supabase
          .from('guardians')
          .select('id')
          .limit(1);
        if (error) throw error;
        
        newResults.push({
          category: 'Guardians',
          status: 'success',
          message: 'Tabela de responsáveis acessível'
        });
      } catch (error: any) {
        newResults.push({
          category: 'Guardians',
          status: 'error',
          message: `Erro ao acessar tabela de responsáveis: ${error.message}`
        });
      }

      // Test 5: Locations Table
      try {
        const { error } = await supabase
          .from('locations')
          .select('id')
          .limit(1);
          
        if (error) throw error;
        
        newResults.push({
          category: 'Locations',
          status: 'success',
          message: 'Tabela de localizações acessível'
        });
      } catch (error: any) {
        newResults.push({
          category: 'Locations',
          status: 'error',
          message: `Erro ao acessar tabela de localizações: ${error.message}`
        });
      }

      // Test 6: Edge Functions
      try {
        const { error } = await supabase.functions.invoke('share-location', {
          body: { test: true }
        });
        if (error) throw error;
        
        newResults.push({
          category: 'Edge Functions',
          status: 'success',
          message: 'Edge function de compartilhamento de localização acessível'
        });
      } catch (error: any) {
        newResults.push({
          category: 'Edge Functions',
          status: 'warning',
          message: `Edge function pode estar offline: ${error.message}`
        });
      }

      // Test 7: RLS Policies
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (session) {
          const { error } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', session.user.id);
            
          if (error) throw error;
          
          newResults.push({
            category: 'RLS Policies',
            status: 'success',
            message: 'Políticas RLS funcionando corretamente'
          });
        } else {
          newResults.push({
            category: 'RLS Policies',
            status: 'warning',
            message: 'Não é possível testar RLS sem usuário autenticado'
          });
        }
      } catch (error: any) {
        newResults.push({
          category: 'RLS Policies',
          status: 'error',
          message: `Erro nas políticas RLS: ${error.message}`
        });
      }

    } catch (error: any) {
      newResults.push({
        category: 'System',
        status: 'error',
        message: `Erro geral do sistema: ${error.message}`
      });
    }

    setResults(newResults);
    setIsRunning(false);
    
    const hasErrors = newResults.some(r => r.status === 'error');
    toast({
      title: hasErrors ? "Diagnóstico concluído com erros" : "Diagnóstico concluído",
      description: `${newResults.length} testes executados`,
      variant: hasErrors ? "destructive" : "default"
    });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <XCircle className="h-5 w-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      warning: 'secondary'
    } as const;
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'default'}>
        {status.toUpperCase()}
      </Badge>
    );
  };

  return (
    <div className="container mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-6 w-6" />
            Ferramenta de Diagnóstico do Sistema
          </CardTitle>
          <p className="text-sm text-gray-600">
            Execute um diagnóstico completo para verificar o funcionamento do sistema EduConnect
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button 
              onClick={runDiagnostics} 
              disabled={isRunning}
              className="w-full"
            >
              {isRunning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Executando Diagnósticos...
                </>
              ) : (
                'Executar Diagnósticos'
              )}
            </Button>

            {results.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Resultados:</h3>
                {results.map((result, index) => (
                  <div 
                    key={index}
                    className="flex items-start justify-between p-3 border rounded-lg"
                  >
                    <div className="flex items-start gap-3 flex-1">
                      {getStatusIcon(result.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">{result.category}</span>
                          {getStatusBadge(result.status)}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{result.message}</p>
                        {result.details && (
                          <details className="text-xs">
                            <summary className="cursor-pointer text-blue-600">
                              Ver detalhes
                            </summary>
                            <pre className="mt-2 p-2 bg-gray-100 rounded overflow-x-auto">
                              {JSON.stringify(result.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DiagnosticTool;
