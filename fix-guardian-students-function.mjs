#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';

// Configuração do Supabase
const supabaseUrl = 'https://rsvjnndhbyyxktbczlnk.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJzdmpubmRoYnl5eGt0YmN6bG5rIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzQwOTc3OSwiZXhwIjoyMDU4OTg1Nzc5fQ.cnmSutfsHLOWHqMpgIOv5fCHBI0jZG4AN5YJSeHDsEA';

// Inicializar cliente Supabase com service key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

console.log('🔧 Iniciando correção da função get_guardian_students...\n');

async function checkCurrentFunction() {
  console.log('1. Verificando função atual...');
  
  try {
    const { data, error } = await supabase
      .from('pg_proc')
      .select('proname, prosrc')
      .eq('proname', 'get_guardian_students');
    
    if (error) {
      console.log('❌ Erro ao verificar função:', error.message);
      return false;
    }
    
    if (data && data.length > 0) {
      console.log('✅ Função get_guardian_students encontrada');
      console.log('📝 Código atual:', data[0].prosrc?.substring(0, 100) + '...');
      return true;
    } else {
      console.log('❌ Função get_guardian_students não encontrada');
      return false;
    }
  } catch (error) {
    console.log('❌ Erro ao verificar função:', error.message);
    return false;
  }
}

async function checkTables() {
  console.log('\n2. Verificando tabelas necessárias...');
  
  // Verificar tabela guardians
  try {
    const { data: guardiansData, error: guardiansError } = await supabase
      .from('guardians')
      .select('count(*)')
      .limit(1);
    
    if (guardiansError) {
      console.log('❌ Tabela guardians não encontrada:', guardiansError.message);
    } else {
      console.log('✅ Tabela guardians encontrada');
    }
  } catch (error) {
    console.log('❌ Erro ao verificar tabela guardians:', error.message);
  }
  
  // Verificar tabela profiles
  try {
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('count(*)')
      .limit(1);
    
    if (profilesError) {
      console.log('❌ Tabela profiles não encontrada:', profilesError.message);
    } else {
      console.log('✅ Tabela profiles encontrada');
    }
  } catch (error) {
    console.log('❌ Erro ao verificar tabela profiles:', error.message);
  }
}

async function createOrUpdateFunction() {
  console.log('\n3. Criando/atualizando função get_guardian_students...');
  
  const functionSQL = `
-- Remove função antiga se existir
DROP FUNCTION IF EXISTS public.get_guardian_students();

-- Cria nova função corrigida
CREATE OR REPLACE FUNCTION public.get_guardian_students()
RETURNS TABLE (
  student_id UUID,
  student_email TEXT,
  student_name TEXT
) 
LANGUAGE plpgsql
STABLE
SECURITY DEFINER
AS $$
BEGIN
  -- Log para debug
  RAISE NOTICE 'get_guardian_students: Executando para usuário %', auth.email();
  
  RETURN QUERY
  SELECT
    u.id::UUID as student_id,
    u.email::TEXT as student_email,
    COALESCE(p.full_name, u.email, 'Sem nome')::TEXT as student_name
  FROM
    public.guardians g
    JOIN auth.users u ON g.student_id = u.id
    LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE
    g.email = auth.email()
    AND g.is_active = true;
END;
$$;

-- Conceder permissões
GRANT EXECUTE ON FUNCTION public.get_guardian_students TO authenticated;

-- Comentário para documentação
COMMENT ON FUNCTION public.get_guardian_students IS 'Retorna estudantes vinculados ao responsável autenticado';
`;

  try {
    const { error } = await supabase.rpc('exec', { sql: functionSQL });
    
    if (error) {
      console.log('❌ Erro ao criar função:', error.message);
      
      // Tentar abordagem alternativa
      console.log('\n🔄 Tentando abordagem alternativa...');
      
      const alternativeSQL = `
CREATE OR REPLACE FUNCTION public.get_guardian_students()
RETURNS TABLE (
  student_id UUID,
  student_email TEXT,
  student_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    u.id,
    u.email,
    COALESCE(p.full_name, 'Estudante')
  FROM
    public.guardians g
    JOIN auth.users u ON g.student_id = u.id
    LEFT JOIN public.profiles p ON p.user_id = u.id
  WHERE
    g.email = (auth.jwt() ->> 'email')
    AND g.is_active = true;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

GRANT EXECUTE ON FUNCTION public.get_guardian_students TO authenticated;
`;
      
      const { error: altError } = await supabase.rpc('exec', { sql: alternativeSQL });
      
      if (altError) {
        console.log('❌ Erro na abordagem alternativa:', altError.message);
        return false;
      } else {
        console.log('✅ Função criada com abordagem alternativa');
        return true;
      }
    } else {
      console.log('✅ Função criada com sucesso');
      return true;
    }
  } catch (error) {
    console.log('❌ Erro ao executar SQL:', error.message);
    return false;
  }
}

async function testFunction() {
  console.log('\n4. Testando função...');
  
  try {
    const { data, error } = await supabase.rpc('get_guardian_students');
    
    if (error) {
      console.log('❌ Erro ao testar função:', error.message);
      console.log('📋 Detalhes do erro:', error);
      return false;
    } else {
      console.log('✅ Função testada com sucesso');
      console.log('📊 Resultado:', data);
      return true;
    }
  } catch (error) {
    console.log('❌ Erro ao testar função:', error.message);
    return false;
  }
}

async function checkSampleData() {
  console.log('\n5. Verificando dados de exemplo...');
  
  try {
    // Verificar se há dados na tabela guardians
    const { data: guardians, error: guardiansError } = await supabase
      .from('guardians')
      .select('*')
      .limit(5);
    
    if (guardiansError) {
      console.log('❌ Erro ao buscar guardians:', guardiansError.message);
    } else {
      console.log(`📊 Encontrados ${guardians?.length || 0} registros na tabela guardians`);
      if (guardians && guardians.length > 0) {
        console.log('📋 Exemplo:', guardians[0]);
      }
    }
    
    // Verificar se há usuários
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('user_id, email, full_name, user_type')
      .eq('user_type', 'student')
      .limit(3);
    
    if (usersError) {
      console.log('❌ Erro ao buscar profiles:', usersError.message);
    } else {
      console.log(`📊 Encontrados ${users?.length || 0} estudantes na tabela profiles`);
      if (users && users.length > 0) {
        console.log('📋 Exemplo:', users[0]);
      }
    }
  } catch (error) {
    console.log('❌ Erro ao verificar dados:', error.message);
  }
}

// Executar todas as verificações
async function main() {
  try {
    await checkCurrentFunction();
    await checkTables();
    await createOrUpdateFunction();
    await testFunction();
    await checkSampleData();
    
    console.log('\n🎉 Processo concluído!');
    console.log('🔗 Teste agora acessando http://localhost:4000/parent-dashboard');
    
  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

main();
