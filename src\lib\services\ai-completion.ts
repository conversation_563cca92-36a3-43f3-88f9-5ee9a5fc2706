/**
 * AI Code Completion Service
 * Based on windsurf.vim architecture patterns
 */

export interface CompletionRequest {
  document: DocumentContext;
  metadata: RequestMetadata;
  otherDocuments?: DocumentContext[];
  editorOptions: EditorOptions;
}

export interface DocumentContext {
  text: string;
  editorLanguage: string;
  language: number;
  cursorPosition: { row: number; col: number };
  absolutePath: string;
  lineEnding?: string;
}

export interface RequestMetadata {
  apiKey: string;
  ideName: string;
  ideVersion: string;
  extensionName: string;
  extensionVersion: string;
  requestId?: string;
}

export interface EditorOptions {
  tabSize: number;
  insertSpaces: boolean;
}

export interface CompletionItem {
  completion: {
    completionId: string;
    text: string;
  };
  range: {
    startOffset: number;
    endOffset: number;
  };
  suffix?: {
    text: string;
    deltaCursorOffset: number;
  };
  completionParts: CompletionPart[];
}

export interface CompletionPart {
  type: 'COMPLETION_PART_TYPE_INLINE' | 'COMPLETION_PART_TYPE_BLOCK' | 'COMPLETION_PART_TYPE_INLINE_MASK';
  text: string;
  line: number;
  prefix?: string;
}

export interface CompletionResponse {
  completionItems: CompletionItem[];
}

// Language enum mapping (from windsurf.vim)
export const LANGUAGE_ENUM = {
  unspecified: 0,
  javascript: 17,
  typescript: 45,
  typescriptreact: 44,
  html: 14,
  css: 6,
  json: 18,
  markdown: 25,
  sql: 41,
  yaml: 50,
  // Add more as needed
} as const;

export const FILETYPE_ALIASES = {
  js: 'javascript',
  ts: 'typescript',
  tsx: 'typescriptreact',
  jsx: 'javascript',
  mjs: 'javascript',
  cjs: 'javascript',
} as const;

export class AICompletionService {
  private apiKey: string;
  private baseUrl: string;
  private requestNonce: number = 0;
  private activeRequests: Map<string, AbortController> = new Map();

  constructor(apiKey: string, baseUrl: string = 'https://server.codeium.com') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  /**
   * Get code completions for the current document context
   */
  async getCompletions(request: CompletionRequest): Promise<CompletionResponse> {
    const requestId = this.generateRequestId();
    const controller = new AbortController();
    
    // Cancel any existing request
    this.cancelActiveRequests();
    this.activeRequests.set(requestId, controller);

    try {
      const response = await fetch(`${this.baseUrl}/exa.language_server_pb.LanguageServerService/GetCompletions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...request,
          metadata: {
            ...request.metadata,
            requestId,
          },
        }),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      this.activeRequests.delete(requestId);
      
      return result;
    } catch (error) {
      this.activeRequests.delete(requestId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request cancelled');
      }
      throw error;
    }
  }

  /**
   * Accept a completion (for analytics/learning)
   */
  async acceptCompletion(completionId: string): Promise<void> {
    try {
      await fetch(`${this.baseUrl}/exa.language_server_pb.LanguageServerService/AcceptCompletion`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metadata: this.getRequestMetadata(),
          completionId,
        }),
      });
    } catch (error) {
      console.warn('Failed to report completion acceptance:', error);
    }
  }

  /**
   * Cancel all active completion requests
   */
  cancelActiveRequests(): void {
    for (const [requestId, controller] of this.activeRequests) {
      controller.abort();
      this.activeRequests.delete(requestId);
    }
  }

  /**
   * Create document context from editor state
   */
  createDocumentContext(
    content: string,
    filePath: string,
    cursorPosition: { row: number; col: number },
    language?: string
  ): DocumentContext {
    const fileExtension = filePath.split('.').pop()?.toLowerCase() || '';
    const detectedLanguage = language || this.detectLanguage(fileExtension);
    
    return {
      text: content,
      editorLanguage: detectedLanguage,
      language: LANGUAGE_ENUM[detectedLanguage as keyof typeof LANGUAGE_ENUM] || LANGUAGE_ENUM.unspecified,
      cursorPosition,
      absolutePath: filePath,
      lineEnding: '\n',
    };
  }

  /**
   * Generate request metadata
   */
  private getRequestMetadata(): RequestMetadata {
    return {
      apiKey: this.apiKey,
      ideName: 'locate-family-connect',
      ideVersion: '1.0.0',
      extensionName: 'ai-completion',
      extensionVersion: '1.0.0',
    };
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    this.requestNonce += 1;
    return `req_${Date.now()}_${this.requestNonce}`;
  }

  /**
   * Detect programming language from file extension
   */
  private detectLanguage(extension: string): string {
    const aliased = FILETYPE_ALIASES[extension as keyof typeof FILETYPE_ALIASES];
    if (aliased) return aliased;
    
    if (extension in LANGUAGE_ENUM) {
      return extension;
    }
    
    return 'unspecified';
  }
}

/**
 * Debounced completion manager
 */
export class DebouncedCompletionManager {
  private service: AICompletionService;
  private debounceMs: number;
  private timeoutId: NodeJS.Timeout | null = null;

  constructor(service: AICompletionService, debounceMs: number = 150) {
    this.service = service;
    this.debounceMs = debounceMs;
  }

  /**
   * Request completions with debouncing
   */
  requestCompletions(
    request: CompletionRequest,
    callback: (response: CompletionResponse | null, error?: Error) => void
  ): void {
    // Clear existing timeout
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }

    // Set new timeout
    this.timeoutId = setTimeout(async () => {
      try {
        const response = await this.service.getCompletions(request);
        callback(response);
      } catch (error) {
        callback(null, error as Error);
      }
    }, this.debounceMs);
  }

  /**
   * Cancel pending requests
   */
  cancel(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    this.service.cancelActiveRequests();
  }
}
