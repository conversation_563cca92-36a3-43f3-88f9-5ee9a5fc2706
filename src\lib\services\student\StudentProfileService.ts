
import { supabase } from '@/lib/supabase';
import { Student } from '@/types/auth';
import { CircuitBreaker } from '@/lib/utils/circuit-breaker';
import { recordApiError } from '@/lib/utils/cache-manager';
import { retryWithBackoff, isSupabaseServiceUnavailable } from '@/lib/utils/network-utils';
import { smartCache } from '@/lib/offline/smart-cache';
import { CacheSeeder } from '@/lib/utils/cache-seeder';

export class StudentProfileService {
  private circuitBreaker: CircuitBreaker;

  constructor() {
    this.circuitBreaker = new CircuitBreaker({
      failureThreshold: 3,
      resetTimeout: 30000, // 30 seconds
      name: 'StudentProfileService'
    });
  }
  async getStudentProfile(studentId: string): Promise<Student | null> {
    return this.circuitBreaker.execute(
      async () => {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', studentId)
          .eq('user_type', 'student')
          .single();

        if (error) {
          console.error('Error fetching student profile:', error);
          recordApiError(error.code === '503' ? 503 : 500, 'student_profile_query');
          throw error;
        }

        return data ? {
          id: data.user_id || '',
          user_id: data.user_id || '',
          name: data.full_name || '',
          email: data.email || '',
          schoolId: '',
          schoolName: '',
          grade: '',
          class: '',
          status: 'active' as const,
          guardianId: '',
          locationSharing: false,
          isActive: true,
          avatarUrl: undefined,
          lastSeen: null,
          lastLocation: null,
          createdAt: data.created_at || '',
          updatedAt: data.updated_at || ''
        } : null;
      },
      async () => {
        console.log('🔄 [StudentProfileService] Circuit breaker fallback para getStudentProfile');
        return null; // Return null as fallback for profile queries
      }
    ).catch(error => {
      console.error('Error in getStudentProfile:', error);
      return null;
    });
  }

  async getStudentsByGuardian(_guardianEmail?: string): Promise<Student[]> {
    console.log('🔍 [StudentProfileService] Iniciando getStudentsByGuardian...');
    console.log('🔍 [StudentProfileService] Guardian email:', _guardianEmail);

    // Get current user for fallback
    const {
      data: { session }
    } = await supabase.auth.getSession();
    const user = session?.user;

    if (!user) {
      console.error('❌ [StudentProfileService] Usuário não autenticado');
      return [];
    }

    console.log('🔍 [StudentProfileService] Usuário autenticado:', {
      id: user.id,
      email: user.email,
      email_confirmed: user.email_confirmed_at ? 'Yes' : 'No'
    });

    // Initialize smart cache
    try {
      await smartCache.init();
    } catch (error) {
      console.warn('⚠️ [StudentProfileService] Cache não disponível:', error);
    }

    return this.circuitBreaker.execute(
      // Main operation
      async () => {
        const students = await this.fetchStudentsViaRPC();
        // Cache successful results
        if (students.length > 0) {
          try {
            await smartCache.cacheStudentList(user.email!, students.map(s => ({
              id: parseInt(s.id) || 0,
              user_id: s.user_id || s.id,
              full_name: s.name,
              email: s.email,
              user_type: 'student',
              created_at: s.createdAt,
              updated_at: s.updatedAt
            })));
            console.log('✅ [StudentProfileService] Dados cacheados com sucesso');
          } catch (cacheError) {
            console.warn('⚠️ [StudentProfileService] Erro ao cachear:', cacheError);
          }
        }
        return students;
      },
      // Fallback operation
      async () => {
        console.log('🔄 [StudentProfileService] Circuit breaker ativou fallback');
        return this.fetchStudentsWithCache(user.email!);
      }
    );
  }

  private async fetchStudentsViaRPC(): Promise<Student[]> {
    try {
      console.log('🔍 [StudentProfileService] Chamando supabase.rpc("get_guardian_students")...');

      // Verify authentication before making RPC call
      const {
        data: { session }
      } = await supabase.auth.getSession();
      const user = session?.user;

      if (!user) {
        console.error('❌ [StudentProfileService] Usuário não autenticado para RPC');
        throw new Error('User not authenticated');
      }

      console.log('🔍 [StudentProfileService] Usuário autenticado:', {
        id: user.id,
        email: user.email,
        email_confirmed: user.email_confirmed_at ? 'Yes' : 'No'
      });

      const { data, error } = await retryWithBackoff(
        async () => {
          const result = await supabase.rpc('get_guardian_students');

          if (result.error && isSupabaseServiceUnavailable(result.error)) {
            console.log('🔄 [StudentProfileService] Serviço indisponível, tentando novamente...');
            throw result.error;
          }

          return result;
        },
        { maxRetries: 2, baseDelay: 1000 }
      );

      console.log('🔍 [StudentProfileService] Resposta da RPC:', { data, error });

      let records = data;

      if (error) {
        console.error('❌ [StudentProfileService] Erro na RPC:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint
        });

        // Enhanced error handling for different error types
        if (error.code === 'PGRST116') {
          console.error('❌ [StudentProfileService] Função get_guardian_students não encontrada');
          recordApiError(404, 'get_guardian_students_rpc_not_found');
        } else if (error.code === '42501') {
          console.error('❌ [StudentProfileService] Erro de permissão na função RPC');
          recordApiError(403, 'get_guardian_students_rpc_permission');
        } else if (error.message?.includes('not authenticated') || error.message?.includes('JWT') || error.message?.includes('User not authenticated')) {
          console.error('❌ [StudentProfileService] Erro de autenticação na função RPC');
          recordApiError(401, 'get_guardian_students_rpc_auth');
        } else if (error.code === '400' || error.message?.includes('400')) {
          console.error('❌ [StudentProfileService] Bad Request - possível problema de parâmetros');
          recordApiError(400, 'get_guardian_students_rpc_bad_request');
        } else {
          console.error('❌ [StudentProfileService] Erro genérico na função RPC');
          recordApiError(error.code === '503' ? 503 : 500, 'get_guardian_students_rpc_generic');
        }

        throw error;
      }

      if (!records) {
        console.log('⚠️ [StudentProfileService] Nenhum dado retornado');
        return [];
      }

      console.log('✅ [StudentProfileService] Dados recebidos:', records);
      console.log('✅ [StudentProfileService] Total de registros:', records.length);

      const mappedStudents = records.map((student: any) => ({
        id: student.student_id,
        user_id: student.student_id,
        name: student.student_name,
        email: student.student_email,
        schoolId: '',
        schoolName: '',
        grade: '',
        class: '',
        status: 'active' as const,
        guardianId: '',
        locationSharing: false,
        isActive: true,
        avatarUrl: undefined,
        lastSeen: null,
        lastLocation: null,
        createdAt: '',
        updatedAt: ''
      }));

      console.log('✅ [StudentProfileService] Estudantes mapeados:', mappedStudents);
      return mappedStudents;
    } catch (error) {
      console.error('❌ [StudentProfileService] Erro na RPC:', error);
      throw error;
    }
  }

  private async fetchStudentsWithCache(userEmail: string): Promise<Student[]> {
    try {
      console.log('🔄 [StudentProfileService] Tentando fallback com cache...');

      // First try to get from cache
      try {
        const cachedStudents = await smartCache.getStudentList(userEmail);
        if (cachedStudents && cachedStudents.length > 0) {
          console.log('📦 [StudentProfileService] Dados encontrados no cache:', cachedStudents.length);
          return cachedStudents.map(cached => ({
            id: cached.user_id || '',
            user_id: cached.user_id || '',
            name: cached.full_name || '',
            email: cached.email || '',
            schoolId: '',
            schoolName: '',
            grade: '',
            class: '',
            status: 'active' as const,
            guardianId: '',
            locationSharing: false,
            isActive: true,
            avatarUrl: undefined,
            lastSeen: null,
            lastLocation: null,
            createdAt: cached.created_at || '',
            updatedAt: cached.updated_at || ''
          }));
        }
      } catch (cacheError) {
        console.warn('⚠️ [StudentProfileService] Erro ao acessar cache:', cacheError);
      }

      // If no cache, try direct query as last resort
      return this.fetchStudentsViaDirectQuery(userEmail);

    } catch (error) {
      console.error('❌ [StudentProfileService] Erro no fallback com cache:', error);

      // As absolute last resort, try to get any cached data or seed with sample data
      try {
        let cachedStudents = await smartCache.getStudentList(userEmail);

        // If no cache exists and service is unavailable, seed with sample data
        if (!cachedStudents || cachedStudents.length === 0) {
          console.log('🌱 [StudentProfileService] Nenhum cache encontrado, populando com dados de exemplo...');
          await CacheSeeder.seedStudentData(userEmail);
          cachedStudents = await smartCache.getStudentList(userEmail);
        }

        if (cachedStudents && cachedStudents.length > 0) {
          console.log('📦 [StudentProfileService] Usando cache como último recurso');
          return cachedStudents.map(cached => ({
            id: cached.user_id || '',
            user_id: cached.user_id || '',
            name: cached.full_name || '',
            email: cached.email || '',
            schoolId: '',
            schoolName: '',
            grade: '',
            class: '',
            status: 'active' as const,
            guardianId: '',
            locationSharing: false,
            isActive: true,
            avatarUrl: undefined,
            lastSeen: null,
            lastLocation: null,
            createdAt: cached.created_at || '',
            updatedAt: cached.updated_at || ''
          }));
        }
      } catch (finalCacheError) {
        console.error('❌ [StudentProfileService] Erro no cache final:', finalCacheError);
      }

      throw error;
    }
  }

  private async fetchStudentsViaDirectQuery(userEmail: string): Promise<Student[]> {
    try {
      console.log('🔄 [StudentProfileService] Usando método fallback direto...');

      const { data: guardiansData, error: guardiansError } = await supabase
        .from('guardians')
        .select('student_id')
        .eq('email', userEmail)
        .eq('is_active', true);

      if (guardiansError) {
        console.error('❌ [StudentProfileService] Erro no fallback:', guardiansError);
        recordApiError(503, 'guardians_table_query');
        throw guardiansError;
      }

      if (!guardiansData || guardiansData.length === 0) {
        console.log('⚠️ [StudentProfileService] Nenhuma relação guardian encontrada');
        return [];
      }

      // Extract student IDs
      const studentIds = guardiansData
        .map(g => g.student_id)
        .filter(id => id !== null);

      console.log('🔍 [StudentProfileService] IDs dos estudantes encontrados:', studentIds);

      if (studentIds.length === 0) {
        return [];
      }

      // Fetch student profiles
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('*')
        .in('user_id', studentIds);

      if (profilesError) {
        console.error('❌ [StudentProfileService] Erro ao buscar perfis:', profilesError);
        recordApiError(503, 'profiles_table_query');
        throw profilesError;
      }

      // Convert profiles to Student format
      const students = profilesData?.map(profile => ({
        id: profile.user_id || '',
        user_id: profile.user_id || '',
        name: profile.full_name || '',
        email: profile.email || '',
        schoolId: '',
        schoolName: '',
        grade: '',
        class: '',
        status: 'active' as const,
        guardianId: '',
        locationSharing: false,
        isActive: true,
        avatarUrl: undefined,
        lastSeen: null,
        lastLocation: null,
        createdAt: profile.created_at || '',
        updatedAt: profile.updated_at || ''
      })) || [];

      console.log('✅ [StudentProfileService] Estudantes do fallback:', students.length);
      return students;

    } catch (error) {
      console.error('❌ [StudentProfileService] Erro no fallback direto:', error);
      throw error;
    }
  }

  // Alias for backward compatibility
  async getStudentsForGuardian(guardianEmail?: string): Promise<Student[]> {
    return this.getStudentsByGuardian(guardianEmail);
  }
}

export const studentProfileService = new StudentProfileService();
