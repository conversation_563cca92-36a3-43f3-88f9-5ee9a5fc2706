/**
 * AI Completion Provider Component
 * Provides AI completion context to child components
 */

import { createContext, useContext, ReactNode } from 'react';
import { useAICompletion, UseAICompletionOptions, AICompletionState, AICompletionActions } from '../../hooks/useAICompletion';

interface AICompletionContextValue {
  state: AICompletionState & { currentSuggestion: any };
  actions: AICompletionActions;
}

const AICompletionContext = createContext<AICompletionContextValue | null>(null);

export interface AICompletionProviderProps {
  children: ReactNode;
  options: UseAICompletionOptions;
}

export function AICompletionProvider({ children, options }: AICompletionProviderProps) {
  const completion = useAICompletion(options);

  return (
    <AICompletionContext.Provider value={completion}>
      {children}
    </AICompletionContext.Provider>
  );
}

export function useAICompletionContext() {
  const context = useContext(AICompletionContext);
  if (!context) {
    throw new Error('useAICompletionContext must be used within an AICompletionProvider');
  }
  return context;
}
