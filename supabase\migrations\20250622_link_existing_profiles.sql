DO $$
DECLARE
  v_guardian_user_id UUID;
  v_student_franklin_id UUID;
  v_student_sarah_id UUID;
  v_student_mauricio_id UUID;
BEGIN
  -- PASSO 1: Obter os IDs de todos os perfis existentes.
  RAISE NOTICE 'Fetching existing user IDs...';
  
  -- Busca o ID do guardião na tabela de perfis.
  SELECT user_id INTO v_guardian_user_id FROM public.profiles WHERE email = '<EMAIL>';
  
  -- <PERSON><PERSON> os IDs dos estudantes.
  SELECT user_id INTO v_student_franklin_id FROM public.profiles WHERE email = '<EMAIL>';
  SELECT user_id INTO v_student_sarah_id FROM public.profiles WHERE email = '<EMAIL>';
  SELECT user_id INTO v_student_mauricio_id FROM public.profiles WHERE email = '<EMAIL>';

  -- PASSO 2: <PERSON><PERSON><PERSON> que todos os perfis foram encontrados.
  IF v_guardian_user_id IS NULL OR v_student_franklin_id IS NULL OR v_student_sarah_id IS NULL OR v_student_mauricio_id IS NULL THEN
    RAISE EXCEPTION 'CRITICAL: One or more user profiles were not found in the "profiles" table. Please verify the emails. Aborting.';
  END IF;
  RAISE NOTICE 'All required profiles exist. Proceeding with linking.';

  -- PASSO 3: Limpar quaisquer associações antigas para estes estudantes para evitar duplicatas.
  RAISE NOTICE 'Deleting old guardian relationships for these students...';
  DELETE FROM public.guardians
  WHERE student_id IN (v_student_franklin_id, v_student_sarah_id, v_student_mauricio_id);
  RAISE NOTICE 'Old relationships cleared.';

  -- PASSO 4: Inserir os vínculos corretos, usando o 'guardian_profile_id' que sabemos que existe.
  RAISE NOTICE 'Inserting new, correct relationships...';
  INSERT INTO public.guardians (student_id, guardian_profile_id, is_active)
  VALUES
    (v_student_franklin_id, v_guardian_user_id, true),
    (v_student_sarah_id, v_guardian_user_id, true),
    (v_student_mauricio_id, v_guardian_user_id, true);
  RAISE NOTICE 'Inserted 3 new relationship rows.';

  RAISE NOTICE 'SUCCESS: Data correction complete. All students are now correctly linked to the existing guardian.';

END;
$$; 