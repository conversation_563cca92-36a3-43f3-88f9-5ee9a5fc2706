# Correção de "Nenhum estudante vinculado"

Este guia resume o procedimento para corrigir o erro em que o dashboard do responsável exibe "Nenhum estudante vinculado", mesmo existindo relacionamentos no banco.

## Passo a passo rápido

1. **Acessar o Supabase**
   - Entre no [painel do projeto](https://app.supabase.com/project/rsvjnndhbyyxktbczlnk/database). 
   - Abra o **SQL Editor**.

2. **Executar o script de correção**
   - Copie o conteúdo do arquivo [`fix-parent-dashboard.sql`](../fix-parent-dashboard.sql).
   - Cole no editor SQL e execute.
   - Isso recria a função `get_guardian_students` com as permissões corretas e garante que as tabelas necessárias existam.

3. **Criar usuários de teste (opcional)**
   - Utilize o arquivo [`create-test-user.html`](../create-test-user.html) para gerar um responsável e um estudante de teste.
   - Em seguida, vincule-os executando o trecho de SQL descrito em [`AGENTS (28).md`](../%27AGENTS%20(28).md).

4. **Testar o dashboard**
   - Reinicie o frontend (`npm run dev`) e acesse `http://localhost:4000/parent-dashboard`.
   - Os estudantes vinculados devem aparecer normalmente.

5. **Usar a página de debug**
   - Abra [`debug-parent-dashboard.html`](../debug-parent-dashboard.html) em seu navegador.
   - Clique em **Testar Função RPC** para verificar se `get_guardian_students` retorna dados.
   - Utilize os logs exibidos na tela para diagnosticar eventuais erros.

Caso o problema persista, verifique os logs do navegador e confirme se a função aparece marcada como **STABLE** em **Database > Functions** no Supabase.
