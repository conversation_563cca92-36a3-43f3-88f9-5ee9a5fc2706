/**
 * Redis Health Check and Functionality Test
 * Following the Anti-Break Protocol validation process
 */

// Load environment variables
require('dotenv').config();

const { createClient } = require('redis');

// Connection setup
const redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  password: process.env.REDIS_PASSWORD
});

// Error handling
redisClient.on('error', (err) => {
  console.error('🔴 Redis Client Error:', err);
  process.exit(1);
});

async function runTests() {
  try {
    console.log('🟡 Starting Redis health checks...');
    
    // Step 1: Connect to Redis
    await redisClient.connect();
    console.log('🟢 Successfully connected to Redis');
    
    // Step 2: Ping Redis
    const pingResult = await redisClient.ping();
    console.log(`🟢 Redis PING: ${pingResult}`);
    
    // Step 3: Test cache operations
    const testKey = 'test:health:check';
    const testValue = { timestamp: new Date().toISOString(), status: 'healthy' };
    
    console.log('🟡 Testing SET operation...');
    await redisClient.set(testKey, JSON.stringify(testValue), { EX: 60 });
    console.log('🟢 SET operation successful');
    
    console.log('🟡 Testing GET operation...');
    const retrievedValue = await redisClient.get(testKey);
    console.log(`🟢 Retrieved value: ${retrievedValue}`);
    
    // Step 4: Test key expiration and TTL
    console.log('🟡 Testing TTL (time-to-live)...');
    const ttl = await redisClient.ttl(testKey);
    console.log(`🟢 TTL for ${testKey}: ${ttl} seconds`);
    
    // Step 5: Test pub/sub
    console.log('🟡 Testing PUB/SUB messaging...');
    const testChannel = 'test:notifications';
    
    // Create a separate subscriber client
    const subClient = redisClient.duplicate();
    await subClient.connect();
    
    // Set up subscription with timeout to prevent hanging
    let messageReceived = false;
    const subscriptionTimeout = setTimeout(() => {
      if (!messageReceived) {
        console.error('🔴 PUB/SUB test timed out - no message received');
        cleanup();
      }
    }, 5000);
    
    // Subscribe to test channel
    await subClient.subscribe(testChannel, (message) => {
      console.log(`🟢 Received message: ${message}`);
      messageReceived = true;
      clearTimeout(subscriptionTimeout);
      
      // Continue with cleanup after message is received
      cleanup();
    });
    
    console.log(`🟡 Subscribed to ${testChannel}, publishing test message...`);
    
    // Publish test message
    await redisClient.publish(testChannel, JSON.stringify({
      type: 'test',
      message: 'Hello from Redis test',
      timestamp: new Date().toISOString()
    }));
    
    console.log('🟢 Test message published');
    
    // Allow subscription to receive message
    // Cleanup happens in the subscription callback
    
    async function cleanup() {
      // Final cleanup
      console.log('🟡 Cleaning up test data...');
      await redisClient.del(testKey);
      await subClient.unsubscribe(testChannel);
      await subClient.quit();
      await redisClient.quit();
      console.log('🟢 All tests completed successfully');
    }
    
  } catch (error) {
    console.error('🔴 Redis test failed:', error);
    await redisClient.quit();
    process.exit(1);
  }
}

// Run the tests
runTests();
