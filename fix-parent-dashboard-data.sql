-- Fix Parent Dashboard: Create sample guardian-student relationships
-- Date: 2025-06-22
-- Issue: Parent dashboard shows no students because no guardian-student relationships exist

-- 1. First, let's check the current table structure
SELECT 
  table_name,
  column_name,
  data_type,
  is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' 
  AND table_name IN ('guardians', 'students', 'profiles', 'student_guardian_relationships')
ORDER BY table_name, ordinal_position;

-- 2. Check current user data
SELECT 
  id,
  email,
  email_confirmed_at,
  created_at
FROM auth.users 
WHERE email = '<EMAIL>';

-- 3. Check current profile data
SELECT 
  user_id,
  email,
  full_name,
  user_type
FROM public.profiles 
WHERE email = '<EMAIL>';

-- 4. Check existing guardian relationships
SELECT * FROM public.guardians WHERE email = '<EMAIL>';

-- 5. Create sample student users if they don't exist
DO $$
DECLARE
  guardian_user_id UUID;
  student1_id UUID;
  student2_id UUID;
BEGIN
  -- Get the guardian user ID
  SELECT id INTO guardian_user_id 
  FROM auth.users 
  WHERE email = '<EMAIL>';
  
  IF guardian_user_id IS NULL THEN
    RAISE EXCEPTION 'Guardian user not found: <EMAIL>';
  END IF;
  
  -- Create sample student 1 if not exists
  INSERT INTO auth.users (
    id,
    email,
    email_confirmed_at,
    created_at,
    updated_at
  )
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW()
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO student1_id;
  
  -- Get student1 ID if it already existed
  IF student1_id IS NULL THEN
    SELECT id INTO student1_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
  END IF;
  
  -- Create sample student 2 if not exists
  INSERT INTO auth.users (
    id,
    email,
    email_confirmed_at,
    created_at,
    updated_at
  )
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW()
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO student2_id;
  
  -- Get student2 ID if it already existed
  IF student2_id IS NULL THEN
    SELECT id INTO student2_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
  END IF;
  
  -- Create profiles for students
  INSERT INTO public.profiles (
    user_id,
    email,
    full_name,
    user_type,
    created_at,
    updated_at
  )
  VALUES 
    (
      student1_id,
      '<EMAIL>',
      'João Silva Lima',
      'student',
      NOW(),
      NOW()
    ),
    (
      student2_id,
      '<EMAIL>',
      'Maria Santos Lima',
      'student',
      NOW(),
      NOW()
    )
  ON CONFLICT (user_id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    updated_at = NOW();
  
  -- Create guardian relationships
  INSERT INTO public.guardians (
    student_id,
    email,
    full_name,
    phone,
    is_active,
    created_at,
    updated_at
  )
  VALUES 
    (
      student1_id,
      '<EMAIL>',
      'Mauro Frank Lima de Lima',
      '(11) 99999-1111',
      true,
      NOW(),
      NOW()
    ),
    (
      student2_id,
      '<EMAIL>',
      'Mauro Frank Lima de Lima',
      '(11) 99999-1111',
      true,
      NOW(),
      NOW()
    )
  ON CONFLICT DO NOTHING;
  
  -- If students table exists, create student records
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'students'
  ) THEN
    INSERT INTO public.students (
      id,
      name,
      school_id,
      school_name,
      grade,
      class,
      guardian_id,
      location_sharing,
      created_at,
      updated_at
    )
    VALUES 
      (
        student1_id,
        'João Silva Lima',
        'ESC001',
        'Escola Municipal São Paulo',
        '8º Ano',
        'A',
        guardian_user_id,
        true,
        NOW(),
        NOW()
      ),
      (
        student2_id,
        'Maria Santos Lima',
        'ESC002',
        'Escola Estadual Maria José',
        '9º Ano',
        'B',
        guardian_user_id,
        true,
        NOW(),
        NOW()
      )
    ON CONFLICT (id) DO UPDATE SET
      name = EXCLUDED.name,
      guardian_id = EXCLUDED.guardian_id,
      updated_at = NOW();
  END IF;
  
  -- Create sample locations for students
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'locations'
  ) THEN
    INSERT INTO public.locations (
      user_id,
      latitude,
      longitude,
      address,
      timestamp,
      shared_with_guardians,
      created_at
    )
    VALUES 
      (
        student1_id,
        -23.5505,
        -46.6333,
        'Escola Municipal São Paulo - SP',
        NOW() - INTERVAL '1 hour',
        true,
        NOW()
      ),
      (
        student2_id,
        -23.5615,
        -46.6565,
        'Escola Estadual Maria José - SP',
        NOW() - INTERVAL '30 minutes',
        true,
        NOW()
      )
    ON CONFLICT DO NOTHING;
  END IF;
  
  RAISE NOTICE 'Sample data created successfully for guardian: %', guardian_user_id;
  RAISE NOTICE 'Student 1 ID: %', student1_id;
  RAISE NOTICE 'Student 2 ID: %', student2_id;
  
END $$;

-- 6. Verify the data was created
SELECT 
  'Guardian relationships' as data_type,
  COUNT(*) as count
FROM public.guardians 
WHERE email = '<EMAIL>'

UNION ALL

SELECT 
  'Student profiles' as data_type,
  COUNT(*) as count
FROM public.profiles 
WHERE user_type = 'student' 
  AND email LIKE '%<EMAIL>'

UNION ALL

SELECT 
  'Auth users (students)' as data_type,
  COUNT(*) as count
FROM auth.users 
WHERE email LIKE '%<EMAIL>';

-- 7. Test the get_guardian_students function
SELECT 
  'Function test' as test_type,
  student_id,
  student_email,
  student_name
FROM public.get_guardian_students();

-- 8. Show final verification
SELECT 
  g.id as guardian_record_id,
  g.student_id,
  g.email as guardian_email,
  g.full_name as guardian_name,
  u.email as student_email,
  p.full_name as student_name,
  g.is_active
FROM public.guardians g
JOIN auth.users u ON g.student_id = u.id
LEFT JOIN public.profiles p ON p.user_id = u.id
WHERE g.email = '<EMAIL>'
ORDER BY g.created_at;
