const CACHE_NAME = 'educonnect-v2';
const STATIC_CACHE = 'educonnect-static-v1';
const DYNAMIC_CACHE = 'educonnect-dynamic-v1';
const HMR_PATH = '/@hmr';

// Recursos para cache imediato
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/src/main.tsx',
  '/src/index.css',
  '/manifest.json'
];

// Instalar service worker
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker...');
  
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      console.log('[SW] Caching static assets');
      return cache.addAll([
        '/',
        '/index.html',
        '/manifest.json',
        '/favicon.ico'
      ]);
    })
  );
  
  // Ativar imediatamente
  self.skipWaiting();
});

// Ativar service worker
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('[SW] Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Controlar todas as abas imediatamente
  return self.clients.claim();
});

// Interceptar requests
self.addEventListener('fetch', (event) => {
  // Ignore HMR requests
  if (event.request.url.includes(HMR_PATH)) {
    return;
  }

  const { request } = event;
  const url = new URL(request.url);
  
  // Ignorar requests de extensões e não-http
  if (!request.url.startsWith('http')) {
    return;
  }
  
  // PROTOCOLO ANTI-QUEBRA: Só processar métodos que podem ser cacheados
  if (!isMethodCacheable(request.method)) {
    console.log('[SW] Skipping non-cacheable method:', request.method, request.url);
    event.respondWith(fetch(request));
    return;
  }
  
  try {
    // Cache-first para recursos estáticos
    if (isStaticAsset(request.url)) {
      event.respondWith(cacheFirst(request));
      return;
    }
    
    // Network-first para APIs
    if (isApiRequest(request.url)) {
      event.respondWith(networkFirst(request));
      return;
    }
    
    // Stale-while-revalidate para páginas
    event.respondWith(staleWhileRevalidate(request));
  } catch (error) {
    console.error('[SW] Error in fetch event handler:', error);
    event.respondWith(new Response('Service Worker Error', { status: 500 }));
  }
});

// PROTOCOLO ANTI-QUEBRA: Verificar se o método pode ser cacheado
function isMethodCacheable(method) {
  return ['GET', 'HEAD', 'OPTIONS'].includes(method.toUpperCase());
}

// Estratégia Cache-First (recursos estáticos)
async function cacheFirst(request) {
  try {
    if (!isMethodCacheable(request.method)) {
      return await fetch(request);
    }
    
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse && networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      await cache.put(request, networkResponse.clone());
      return networkResponse;
    }
    
    throw new Error('Network response was not ok');
  } catch (error) {
    console.error('[SW] Cache-first failed:', error);
    return new Response(JSON.stringify({
      error: 'Service Unavailable',
      message: 'Content not available offline'
    }), { 
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  }
}

// Estratégia Network-First (APIs)
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (!networkResponse || !networkResponse.ok) {
      throw new Error('Network response was not ok');
    }
    
    if (isMethodCacheable(request.method)) {
      const cache = await caches.open(DYNAMIC_CACHE);
      await cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.log('[SW] Network failed, trying cache:', error);
    
    if (isMethodCacheable(request.method)) {
      const cachedResponse = await caches.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
    }
    
    return new Response(JSON.stringify({
      error: 'Service Unavailable',
      message: 'Network request failed and no cached version available'
    }), {
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  }
}

// Estratégia Stale-While-Revalidate (páginas)
async function staleWhileRevalidate(request) {
  try {
    // PROTOCOLO ANTI-QUEBRA: Verificação de método antes de qualquer operação de cache
    if (!isMethodCacheable(request.method)) {
      return fetch(request);
    }

    const cache = await caches.open(DYNAMIC_CACHE);
    const cachedResponse = await cache.match(request);

    // Buscar nova versão em background
    const fetchPromise = fetch(request).then((networkResponse) => {
      // PROTOCOLO ANTI-QUEBRA: Verificação dupla antes de cachear
      if (networkResponse.ok && isMethodCacheable(request.method)) {
        cache.put(request, networkResponse.clone());
      }
      return networkResponse;
    }).catch((error) => {
      console.error('[SW] staleWhileRevalidate fetch failed:', error);
      return undefined;
    });

    // Retornar cache imediatamente se disponível
    if (cachedResponse) {
      return cachedResponse;
    }

    // Se não houver cache, aguarde o fetchPromise
    const networkResponse = await fetchPromise;
    if (networkResponse instanceof Response) {
      return networkResponse;
    }

    // Se tudo falhar, retorne um Response de erro
    return new Response(JSON.stringify({
      error: 'Service Unavailable',
      message: 'Content not available offline'
    }), {
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  } catch (error) {
    console.error('[SW] staleWhileRevalidate failed:', error);
    return new Response(JSON.stringify({
      error: 'Service Worker Error',
      message: 'Unexpected error in staleWhileRevalidate'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-store'
      }
    });
  }
}

// Utilitários
function isStaticAsset(url) {
  return url.includes('.css') || 
         url.includes('.js') || 
         url.includes('.png') || 
         url.includes('.jpg') || 
         url.includes('.svg') || 
         url.includes('.ico') ||
         url.includes('/icons/');
}

function isApiRequest(url) {
  return url.includes('/api/') || 
         url.includes('supabase.co') ||
         url.includes('/auth/') ||
         url.includes('/rest/');
}

// Mensagens do cliente
self.addEventListener('message', (event) => {
  console.log('[SW] Message received:', event.data);
  
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});
