/**
 * Debug Parent Dashboard Page
 * Helps diagnose and fix issues with the parent dashboard
 */

import { useState } from 'react';
import PageContainer from '../components/PageContainer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Alert, AlertDescription } from '../components/ui/alert';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { CreateTestData } from '../components/debug/CreateTestData';
import { supabase } from '../lib/supabase';
import { useUser } from '../contexts/UnifiedAuthContext';
import {
  Bug,
  Database,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  RefreshCw,
  TestTube,
  Settings
} from 'lucide-react';

interface DiagnosticResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export function DebugParentDashboard() {
  const { user } = useUser();
  const [isRunning, setIsRunning] = useState(false);
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);

  const runDiagnostics = async () => {
    if (!user) return;

    setIsRunning(true);
    const results: DiagnosticResult[] = [];

    try {
      // Test 1: Check user authentication
      results.push({
        test: 'Autenticação do Usuário',
        status: user ? 'pass' : 'fail',
        message: user ? `Usuário autenticado: ${user.email}` : 'Usuário não autenticado',
        details: {
          id: user?.id,
          email: user?.email,
          user_type: user?.user_metadata?.user_type
        }
      });

      // Test 2: Check profile exists
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', user.id)
        .single();

      results.push({
        test: 'Perfil do Usuário',
        status: profile && !profileError ? 'pass' : 'fail',
        message: profile ? `Perfil encontrado: ${profile.full_name}` : `Erro no perfil: ${profileError?.message}`,
        details: profile
      });

      // Test 3: Test get_guardian_students RPC function
      const { data: students, error: rpcError } = await supabase.rpc('get_guardian_students');

      results.push({
        test: 'Função RPC get_guardian_students',
        status: !rpcError ? 'pass' : 'fail',
        message: !rpcError 
          ? `Função executada com sucesso. Retornou ${students?.length || 0} estudantes`
          : `Erro na função RPC: ${rpcError.message}`,
        details: { students, error: rpcError }
      });

      // Test 4: Check guardian relationships
      const { data: guardians, error: guardianError } = await supabase
        .from('guardians')
        .select('*')
        .eq('email', user.email || '');

      results.push({
        test: 'Relacionamentos de Responsável',
        status: guardians && guardians.length > 0 ? 'pass' : 'warning',
        message: guardians 
          ? `Encontrados ${guardians.length} relacionamentos`
          : `Erro ao buscar relacionamentos: ${guardianError?.message}`,
        details: guardians
      });

      // Test 5: Check table structure (simplified)
      try {
        const { error: tableError } = await supabase
          .from('guardians')
          .select('*')
          .limit(1);

        results.push({
          test: 'Estrutura das Tabelas',
          status: !tableError ? 'pass' : 'fail',
          message: !tableError
            ? 'Estrutura das tabelas verificada'
            : `Erro ao verificar estrutura: ${tableError.message}`,
          details: { accessible: !tableError }
        });
      } catch (error) {
        results.push({
          test: 'Estrutura das Tabelas',
          status: 'fail',
          message: `Erro ao verificar estrutura: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
          details: error
        });
      }

      // Test 6: Check locations
      const { data: locations, error: locationError } = await supabase
        .from('locations')
        .select('*')
        .limit(5);

      results.push({
        test: 'Dados de Localização',
        status: !locationError ? 'pass' : 'fail',
        message: !locationError 
          ? `Encontradas ${locations?.length || 0} localizações na base`
          : `Erro ao buscar localizações: ${locationError.message}`,
        details: locations
      });

    } catch (error) {
      results.push({
        test: 'Erro Geral',
        status: 'fail',
        message: `Erro durante diagnóstico: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
        details: error
      });
    }

    setDiagnostics(results);
    setIsRunning(false);
  };

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusBadge = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass':
        return <Badge variant="default" className="bg-green-600">Passou</Badge>;
      case 'fail':
        return <Badge variant="destructive">Falhou</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-600">Aviso</Badge>;
    }
  };

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Bug className="h-8 w-8 text-primary" />
              Debug Parent Dashboard
            </h1>
            <p className="text-muted-foreground mt-2">
              Diagnostique e corrija problemas com o dashboard do responsável
            </p>
          </div>
          <Button onClick={runDiagnostics} disabled={isRunning}>
            {isRunning ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Executando...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Executar Diagnóstico
              </>
            )}
          </Button>
        </div>

        {/* Current Issue Alert */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Problema Atual:</strong> O dashboard do responsável mostra "Nenhum estudante vinculado encontrado" 
            mesmo quando existem relacionamentos no banco de dados. Use as ferramentas abaixo para diagnosticar e corrigir.
          </AlertDescription>
        </Alert>

        {/* Tabs */}
        <Tabs defaultValue="diagnostics">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="diagnostics" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              Diagnósticos
            </TabsTrigger>
            <TabsTrigger value="testdata" className="flex items-center gap-2">
              <TestTube className="h-4 w-4" />
              Dados de Teste
            </TabsTrigger>
            <TabsTrigger value="solutions" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Soluções
            </TabsTrigger>
          </TabsList>

          <TabsContent value="diagnostics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Resultados do Diagnóstico</CardTitle>
                <CardDescription>
                  Verificação automática dos componentes do sistema
                </CardDescription>
              </CardHeader>
              <CardContent>
                {diagnostics.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    Clique em "Executar Diagnóstico" para verificar o sistema
                  </div>
                ) : (
                  <div className="space-y-4">
                    {diagnostics.map((result, index) => (
                      <div key={index} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(result.status)}
                            <span className="font-medium">{result.test}</span>
                          </div>
                          {getStatusBadge(result.status)}
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">
                          {result.message}
                        </p>
                        {result.details && (
                          <details className="text-xs">
                            <summary className="cursor-pointer font-medium">
                              Ver detalhes
                            </summary>
                            <pre className="mt-2 bg-muted p-2 rounded overflow-auto">
                              {JSON.stringify(result.details, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="testdata">
            <CreateTestData />
          </TabsContent>

          <TabsContent value="solutions" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Soluções Comuns</CardTitle>
                <CardDescription>
                  Passos para resolver problemas identificados
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h4 className="font-medium">1. Nenhum estudante encontrado</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Se a função RPC retorna array vazio, use a aba "Dados de Teste" para criar estudantes de exemplo.
                    </p>
                  </div>

                  <div className="border-l-4 border-yellow-500 pl-4">
                    <h4 className="font-medium">2. Erro na função RPC</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Se a função get_guardian_students falha, verifique as permissões no Supabase Dashboard.
                    </p>
                  </div>

                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-medium">3. Relacionamentos incorretos</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Verifique se o email do responsável na tabela guardians corresponde ao email do usuário logado.
                    </p>
                  </div>

                  <div className="border-l-4 border-red-500 pl-4">
                    <h4 className="font-medium">4. Problemas de autenticação</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Se o usuário não está autenticado corretamente, faça logout e login novamente.
                    </p>
                  </div>
                </div>

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Próximos passos:</strong>
                    <ol className="list-decimal list-inside mt-2 space-y-1 text-sm">
                      <li>Execute o diagnóstico para identificar problemas</li>
                      <li>Crie dados de teste se não houver estudantes</li>
                      <li>Verifique o dashboard do responsável em /parent-dashboard</li>
                      <li>Se o problema persistir, verifique os logs do navegador</li>
                    </ol>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  );
}
