{"mcpServers": {"supabase": {"command": "cmd", "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]}, "context7": {"command": "cmd", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"]}, "browserTools": {"command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"]}, "githubMcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@smithery-ai/github", "--config", "****************************************"]}}, "env": {"ANTHROPIC_API_KEY": "************************************************************************************************************"}}