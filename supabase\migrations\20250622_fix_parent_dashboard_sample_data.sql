-- Fix Parent Dashboard: Create sample guardian-student relationships
-- Date: 2025-06-22
-- Issue: Parent dashboard shows no students because no guardian-student relationships exist

-- Create sample student users and guardian relationships for testing
DO $$
DECLARE
  guardian_user_id UUID;
  student1_id UUID;
  student2_id UUID;
BEGIN
  -- Get the guardian user ID
  SELECT id INTO guardian_user_id 
  FROM auth.users 
  WHERE email = '<EMAIL>';
  
  IF guardian_user_id IS NULL THEN
    RAISE NOTICE 'Guardian user not found: <EMAIL>';
    RETURN;
  END IF;
  
  RAISE NOTICE 'Found guardian user: %', guardian_user_id;
  
  -- Create sample student 1 if not exists
  INSERT INTO auth.users (
    id,
    email,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_user_meta_data
  )
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW(),
    '{"user_type": "student"}'::jsonb
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO student1_id;
  
  -- Get student1 ID if it already existed
  IF student1_id IS NULL THEN
    SELECT id INTO student1_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
  END IF;
  
  -- Create sample student 2 if not exists
  INSERT INTO auth.users (
    id,
    email,
    email_confirmed_at,
    created_at,
    updated_at,
    raw_user_meta_data
  )
  VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    NOW(),
    NOW(),
    NOW(),
    '{"user_type": "student"}'::jsonb
  )
  ON CONFLICT (email) DO NOTHING
  RETURNING id INTO student2_id;
  
  -- Get student2 ID if it already existed
  IF student2_id IS NULL THEN
    SELECT id INTO student2_id 
    FROM auth.users 
    WHERE email = '<EMAIL>';
  END IF;
  
  RAISE NOTICE 'Student 1 ID: %', student1_id;
  RAISE NOTICE 'Student 2 ID: %', student2_id;
  
  -- Create profiles for students
  INSERT INTO public.profiles (
    user_id,
    email,
    full_name,
    user_type,
    created_at,
    updated_at
  )
  VALUES 
    (
      student1_id,
      '<EMAIL>',
      'João Silva Lima',
      'student',
      NOW(),
      NOW()
    ),
    (
      student2_id,
      '<EMAIL>',
      'Maria Santos Lima',
      'student',
      NOW(),
      NOW()
    )
  ON CONFLICT (user_id) DO UPDATE SET
    full_name = EXCLUDED.full_name,
    updated_at = NOW();
  
  RAISE NOTICE 'Created student profiles';
  
  -- Create guardian relationships
  INSERT INTO public.guardians (
    student_id,
    email,
    full_name,
    phone,
    is_active,
    created_at,
    updated_at
  )
  VALUES 
    (
      student1_id,
      '<EMAIL>',
      'Mauro Frank Lima de Lima',
      '(11) 99999-1111',
      true,
      NOW(),
      NOW()
    ),
    (
      student2_id,
      '<EMAIL>',
      'Mauro Frank Lima de Lima',
      '(11) 99999-1111',
      true,
      NOW(),
      NOW()
    )
  ON CONFLICT DO NOTHING;
  
  RAISE NOTICE 'Created guardian relationships';
  
  -- If students table exists, create student records
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'students'
  ) THEN
    INSERT INTO public.students (
      id,
      name,
      school_id,
      school_name,
      grade,
      class,
      guardian_id,
      location_sharing,
      created_at,
      updated_at
    )
    VALUES 
      (
        student1_id,
        'João Silva Lima',
        'ESC001',
        'Escola Municipal São Paulo',
        '8º Ano',
        'A',
        guardian_user_id,
        true,
        NOW(),
        NOW()
      ),
      (
        student2_id,
        'Maria Santos Lima',
        'ESC002',
        'Escola Estadual Maria José',
        '9º Ano',
        'B',
        guardian_user_id,
        true,
        NOW(),
        NOW()
      )
    ON CONFLICT (id) DO UPDATE SET
      name = EXCLUDED.name,
      guardian_id = EXCLUDED.guardian_id,
      updated_at = NOW();
    
    RAISE NOTICE 'Created student records in students table';
  END IF;
  
  -- Create sample locations for students
  IF EXISTS (
    SELECT 1 FROM information_schema.tables 
    WHERE table_schema = 'public' AND table_name = 'locations'
  ) THEN
    INSERT INTO public.locations (
      user_id,
      latitude,
      longitude,
      address,
      timestamp,
      shared_with_guardians,
      created_at
    )
    VALUES 
      (
        student1_id,
        -23.5505,
        -46.6333,
        'Escola Municipal São Paulo - SP',
        NOW() - INTERVAL '1 hour',
        true,
        NOW()
      ),
      (
        student2_id,
        -23.5615,
        -46.6565,
        'Escola Estadual Maria José - SP',
        NOW() - INTERVAL '30 minutes',
        true,
        NOW()
      )
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Created sample locations';
  END IF;
  
  RAISE NOTICE 'Sample data creation completed successfully!';
  
END $$;
