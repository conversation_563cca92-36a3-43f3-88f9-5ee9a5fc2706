{"name": "educonnect-auth-system", "version": "1.0.0", "description": "EduConnect Authentication System", "type": "module", "scripts": {"dev": "vite --port 4000", "build": "tsc && vite build", "build:dev": "vite build --mode development", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "start": "serve -s dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "seed:test-user": "node src/lib/db/seeds/seed_test_user.mjs", "supabase:build": "supabase functions build share-location", "supabase:deploy": "supabase functions deploy share-location", "supabase:serve": "supabase functions serve share-location", "cypress:open": "cypress open", "cypress:run": "cypress run", "cypress:component": "cypress open --component"}, "dependencies": {"@agentdeskai/browser-tools-server": "^1.1.0", "@hookform/resolvers": "^5.0.1", "@mapbox/mapbox-gl-geocoder": "^5.0.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.39.7", "@tanstack/react-query": "^5.24.1", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cmdk": "^1.1.1", "date-fns": "^3.0.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.503.0", "mapbox-gl": "^3.11.1", "monaco-editor": "^0.52.2", "next-themes": "^0.4.6", "postgres": "^3.4.5", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.56.1", "react-resizable-panels": "^2.1.8", "react-router-dom": "^6.22.1", "recharts": "^2.15.3", "redis": "^5.1.1", "resend": "^4.5.0", "sonner": "^2.0.3", "swagger-ui-react": "^5.24.2", "tailwind-merge": "^3.2.0", "task-master-ai": "^0.12.1", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.9.0", "@swc/core": "^1.4.8", "@swc/jest": "^0.2.38", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^14.2.1", "@testing-library/user-event": "^14.5.2", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/mapbox-gl": "^3.4.1", "@types/node": "^20.19.1", "@types/pg": "^8.11.13", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/swagger-ui-react": "^4.18.3", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.17", "cypress": "^14.3.2", "drizzle-kit": "^0.31.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^15.9.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "pg": "^8.16.2", "postcss": "^8.4.35", "serve": "^14.2.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.3.4", "typescript": "^5.2.2", "typescript-eslint": "^8.0.1", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}, "repository": {"type": "git", "url": "git+https://github.com/FrankWebber33/educonnect-auth-system.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/FrankWebber33/educonnect-auth-system/issues"}, "homepage": "https://github.com/FrankWebber33/educonnect-auth-system#readme"}