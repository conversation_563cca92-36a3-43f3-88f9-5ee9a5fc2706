// @ts-check
import { createClient } from 'redis';
import dotenv from 'dotenv';
import { setTimeout } from 'timers/promises';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Configurar variáveis de ambiente
const __dirname = dirname(fileURLToPath(import.meta.url));
const envPath = join(__dirname, '..', '.env');

if (fs.existsSync(envPath)) {
  console.log(`✅ Carregando variáveis de ambiente de ${envPath}`);
  dotenv.config({ path: envPath });
} else {
  console.warn(`⚠️ Arquivo .env não encontrado em ${envPath}`);
  dotenv.config();
}

// Configurações do Redis
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';
const REDIS_PASSWORD = process.env.REDIS_PASSWORD;

// Mock da localização
const mockLocation = {
  studentId: 'test-student-123',
  latitude: -23.5505,
  longitude: -46.6333,
  address: 'Av. Paulista, São Paulo',
  timestamp: new Date().toISOString()
};

// Canais de notificação
const LOCATION_CHANNEL = 'guardian:notifications:location';
const STUDENT_LOCATION_CACHE_KEY = `student:location:${mockLocation.studentId}:guardian`;

/**
 * Teste integrado de Redis Cache e Notificações
 */
async function runIntegratedTest() {
  console.log('\n🔄 INICIANDO TESTE INTEGRADO DE REDIS CACHE E NOTIFICAÇÕES');
  console.log('=======================================================\n');

  // Criar cliente Redis para operações gerais
  const redisClient = createClient({
    url: REDIS_URL,
    password: REDIS_PASSWORD
  });

  // Criar cliente separado para subscriber (precisa ser uma conexão dedicada)
  const subscriberClient = createClient({
    url: REDIS_URL,
    password: REDIS_PASSWORD
  });

  try {
    // Conectar os clientes
    await redisClient.connect();
    console.log('✅ Cliente Redis conectado');
    
    await subscriberClient.connect();
    console.log('✅ Cliente Subscriber conectado');

    // 1. VERIFICAR CONEXÃO
    const pingResponse = await redisClient.ping();
    console.log(`\n🔍 Teste de ping: ${pingResponse}`);

    // 2. LIMPAR DADOS DE TESTE ANTERIORES
    console.log('\n🧹 Limpando dados de teste anteriores...');
    await redisClient.del(STUDENT_LOCATION_CACHE_KEY);
    
    // 3. CONFIGURAR ASSINANTE PARA NOTIFICAÇÕES
    console.log('\n📡 Configurando assinante para canal de notificações...');
    await subscriberClient.subscribe(LOCATION_CHANNEL, (message) => {
      try {
        const notification = JSON.parse(message);
        console.log('\n📨 NOTIFICAÇÃO RECEBIDA:');
        console.log('------------------------');
        console.log('Tipo:', notification.type);
        console.log('Estudante:', notification.studentId);
        console.log('Localização:', `${notification.latitude}, ${notification.longitude}`);
        console.log('Endereço:', notification.address);
        console.log('Timestamp:', notification.timestamp);
        console.log('------------------------');
      } catch (error) {
        console.error('❌ Erro ao processar notificação:', error);
        console.log('Mensagem original:', message);
      }
    });
    console.log(`✅ Assinando canal: ${LOCATION_CHANNEL}`);

    // 4. SIMULAR ARMAZENAMENTO EM CACHE
    console.log('\n💾 Simulando armazenamento em cache...');
    const locationData = {
      id: `loc-${Date.now()}`,
      user_id: mockLocation.studentId,
      latitude: mockLocation.latitude,
      longitude: mockLocation.longitude,
      address: mockLocation.address,
      timestamp: mockLocation.timestamp,
      shared_with_guardians: true
    };

    await redisClient.set(
      STUDENT_LOCATION_CACHE_KEY, 
      JSON.stringify([locationData]),
      { EX: 300 } // TTL de 5 minutos
    );
    console.log('✅ Dados de localização armazenados em cache');

    // 5. VERIFICAR SE O CACHE ESTÁ FUNCIONANDO
    console.log('\n🔍 Verificando dados em cache...');
    const cachedData = await redisClient.get(STUDENT_LOCATION_CACHE_KEY);
    
    if (cachedData) {
      console.log('✅ Dados encontrados no cache:');
      const parsedData = JSON.parse(cachedData);
      console.log(`   - Estudante: ${parsedData[0].user_id}`);
      console.log(`   - Localização: ${parsedData[0].latitude}, ${parsedData[0].longitude}`);
      console.log(`   - Endereço: ${parsedData[0].address}`);
    } else {
      console.log('❌ Dados não encontrados no cache');
    }

    // 6. VERIFICAR TTL
    const ttl = await redisClient.ttl(STUDENT_LOCATION_CACHE_KEY);
    console.log(`✅ TTL para chave de cache: ${ttl} segundos`);

    // 7. SIMULAR NOTIFICAÇÃO DE NOVA LOCALIZAÇÃO
    console.log('\n📣 Publicando notificação de nova localização...');
    const notificationPayload = {
      type: 'location_update',
      studentId: mockLocation.studentId,
      latitude: mockLocation.latitude,
      longitude: mockLocation.longitude,
      address: mockLocation.address,
      timestamp: new Date().toISOString()
    };

    const publishResult = await redisClient.publish(
      LOCATION_CHANNEL,
      JSON.stringify(notificationPayload)
    );

    console.log(`✅ Notificação publicada para ${publishResult} assinantes`);

    // 8. ESPERAR RECEBIMENTO DA NOTIFICAÇÃO (atraso artificial)
    console.log('\n⏳ Aguardando processamento da notificação...');
    await setTimeout(1000);

    // 9. INVALIDAR CACHE
    console.log('\n🗑️ Invalidando cache...');
    await redisClient.del(STUDENT_LOCATION_CACHE_KEY);
    console.log('✅ Cache invalidado');

    // 10. VERIFICAR INVALIDAÇÃO
    const cachedAfterInvalidation = await redisClient.get(STUDENT_LOCATION_CACHE_KEY);
    console.log(`✅ Cache após invalidação: ${cachedAfterInvalidation === null ? 'Vazio (correto)' : 'Ainda existe (incorreto)'}`);

    // 11. RESUMO DE SAÚDE DO REDIS
    console.log('\n📊 RESUMO DE SAÚDE DO REDIS:');
    console.log('------------------------');
    const info = await redisClient.info();
    const memoryMatch = info.match(/used_memory_human:(\S+)/);
    const clientsMatch = info.match(/connected_clients:(\S+)/);
    const uptimeMatch = info.match(/uptime_in_seconds:(\S+)/);
    
    if (memoryMatch) console.log(`Memória utilizada: ${memoryMatch[1]}`);
    if (clientsMatch) console.log(`Clientes conectados: ${clientsMatch[1]}`);
    if (uptimeMatch) console.log(`Uptime: ${uptimeMatch[1]} segundos`);
    console.log('------------------------');

  } catch (error) {
    console.error('\n❌ ERRO NO TESTE:', error);
  } finally {
    // Cancelar inscrição e desconectar clientes
    console.log('\n🔌 Limpando recursos...');
    
    try {
      await subscriberClient.unsubscribe(LOCATION_CHANNEL);
      console.log('✅ Cancelada inscrição no canal de notificações');
    } catch (e) {
      console.error('❌ Erro ao cancelar inscrição:', e);
    }
    
    try {
      await subscriberClient.disconnect();
      console.log('✅ Cliente Subscriber desconectado');
    } catch (e) {
      console.error('❌ Erro ao desconectar subscriber:', e);
    }
    
    try {
      await redisClient.disconnect();
      console.log('✅ Cliente Redis desconectado');
    } catch (e) {
      console.error('❌ Erro ao desconectar cliente Redis:', e);
    }
    
    console.log('\n✅ TESTE CONCLUÍDO');
  }
}

// Executar o teste
runIntegratedTest().catch(console.error);
