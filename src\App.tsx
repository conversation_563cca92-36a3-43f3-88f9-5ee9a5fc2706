
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from '@/components/ui/toaster';
// UserProvider import removed
import AppLayout from '@/layouts/AppLayout';
import Login from '@/pages/Login';
import Register from '@/pages/Register';
import Index from '@/pages/Index';
import StudentDashboard from '@/pages/StudentDashboard';
import ParentDashboard from '@/pages/ParentDashboard';
import StudentMap from '@/pages/StudentMap';
import ProfilePage from '@/pages/ProfilePage';
import LGPDSettings from '@/pages/LGPDSettings';
import GuardiansPage from '@/pages/GuardiansPage';
import { AICodeCompletion } from '@/pages/AICodeCompletion';
import { DebugParentDashboard } from '@/pages/DebugParentDashboard';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { StudentManagementPage } from './pages/parent/StudentManagement';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 30, // 30 minutes
      retry: 1,
    },
  },
});

function App() {
  return (
    // UserProvider wrapper removed
    <QueryClientProvider client={queryClient}>
      <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>
        <Routes>
          {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            
            {/* Protected routes */}
            <Route element={<AppLayout />}>
              <Route path="/student-dashboard" element={<StudentDashboard />} />
              <Route path="/parent-dashboard" element={<ParentDashboard />} />
              <Route path="/parent-dashboard/students" element={<StudentManagementPage />} />
              <Route path="/student-map" element={<StudentMap />} />
              <Route path="/student-map/:id" element={<StudentMap />} />
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/lgpd-settings" element={<LGPDSettings />} />
              <Route path="/guardians" element={<GuardiansPage />} />
              <Route path="/ai-completion" element={<AICodeCompletion />} />
              <Route path="/debug-parent-dashboard" element={<DebugParentDashboard />} />
              
              {/* Legacy redirects */}
              <Route path="/dashboard" element={<Navigate to="/student-dashboard" replace />} />
            </Route>
            
            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
          <Toaster />
        </Router>
      </QueryClientProvider>
    // UserProvider wrapper removed
  );
}

export default App;
