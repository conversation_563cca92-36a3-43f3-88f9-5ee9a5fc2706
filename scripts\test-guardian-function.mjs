#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to test the guardian locations function
 */

import { createClient } from '@supabase/supabase-js';

// Load environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - VITE_SUPABASE_URL');
  console.error('   - VITE_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testGuardianFunction() {
  try {
    console.log('🧪 Testing guardian locations function...');
    
    // Test with a dummy UUID to see if function exists
    const { data, error } = await supabase.rpc(
      'get_student_locations_for_guardian',
      { input_guardian_id: '00000000-0000-0000-0000-000000000000' }
    );
    
    if (error) {
      if (error.code === 'PGRST202') {
        console.log('❌ Function does not exist yet. Please apply the migration first.');
        console.log('💡 Run: node scripts/apply-guardian-migration.mjs');
      } else {
        console.log('✅ Function exists! Error is expected for dummy UUID:', error.message);
      }
    } else {
      console.log('✅ Function exists and returned data:', data);
    }
    
    // Test the database structure
    console.log('\n🔍 Checking database structure...');
    
    const { data: guardiansData, error: guardiansError } = await supabase
      .from('guardians')
      .select('*')
      .limit(1);
    
    if (guardiansError) {
      console.log('❌ Guardians table issue:', guardiansError.message);
    } else {
      console.log('✅ Guardians table accessible');
    }
    
    const { data: locationsData, error: locationsError } = await supabase
      .from('locations')
      .select('*')
      .limit(1);
    
    if (locationsError) {
      console.log('❌ Locations table issue:', locationsError.message);
    } else {
      console.log('✅ Locations table accessible');
    }
    
    const { data: profilesData, error: profilesError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (profilesError) {
      console.log('❌ Profiles table issue:', profilesError.message);
    } else {
      console.log('✅ Profiles table accessible');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testGuardianFunction();
