/**
 * AI Completion Configuration Management
 * Handles API keys, settings, and user preferences
 */

export interface AICompletionConfig {
  enabled: boolean;
  apiKey: string;
  baseUrl: string;
  debounceMs: number;
  maxSuggestions: number;
  enabledFileTypes: string[];
  disabledFileTypes: string[];
  autoTrigger: boolean;
  showInlinePreview: boolean;
  acceptOnTab: boolean;
  acceptOnEnter: boolean;
  telemetryEnabled: boolean;
}

export const DEFAULT_CONFIG: AICompletionConfig = {
  enabled: true,
  apiKey: '',
  baseUrl: 'https://server.codeium.com',
  debounceMs: 150,
  maxSuggestions: 5,
  enabledFileTypes: [
    'javascript',
    'typescript',
    'typescriptreact',
    'html',
    'css',
    'json',
    'markdown',
    'sql',
    'yaml'
  ],
  disabledFileTypes: [
    'gitcommit',
    'gitrebase',
    'help'
  ],
  autoTrigger: true,
  showInlinePreview: true,
  acceptOnTab: true,
  acceptOnEnter: false,
  telemetryEnabled: true,
};

export class AICompletionConfigManager {
  private static readonly STORAGE_KEY = 'ai-completion-config';
  private static readonly API_KEY_STORAGE_KEY = 'ai-completion-api-key';
  private config: AICompletionConfig;
  private listeners: Set<(config: AICompletionConfig) => void> = new Set();

  constructor() {
    this.config = this.loadConfig();
  }

  /**
   * Get current configuration
   */
  getConfig(): AICompletionConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<AICompletionConfig>): void {
    const newConfig = { ...this.config, ...updates };
    this.config = newConfig;
    this.saveConfig();
    this.notifyListeners();
  }

  /**
   * Set API key securely
   */
  setApiKey(apiKey: string): void {
    // Store API key separately for security
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(AICompletionConfigManager.API_KEY_STORAGE_KEY, apiKey);
        this.config.apiKey = apiKey;
        this.notifyListeners();
      } catch (error) {
        console.error('Failed to store API key:', error);
        throw new Error('Failed to store API key securely');
      }
    }
  }

  /**
   * Get API key
   */
  getApiKey(): string {
    if (typeof window !== 'undefined') {
      try {
        return localStorage.getItem(AICompletionConfigManager.API_KEY_STORAGE_KEY) || '';
      } catch (error) {
        console.error('Failed to retrieve API key:', error);
        return '';
      }
    }
    return '';
  }

  /**
   * Check if API key is configured
   */
  hasApiKey(): boolean {
    return this.getApiKey().length > 0;
  }

  /**
   * Clear API key
   */
  clearApiKey(): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem(AICompletionConfigManager.API_KEY_STORAGE_KEY);
        this.config.apiKey = '';
        this.notifyListeners();
      } catch (error) {
        console.error('Failed to clear API key:', error);
      }
    }
  }

  /**
   * Check if completion is enabled for file type
   */
  isEnabledForFileType(fileType: string): boolean {
    if (!this.config.enabled) return false;
    
    // Check disabled list first
    if (this.config.disabledFileTypes.includes(fileType)) {
      return false;
    }
    
    // Check enabled list
    return this.config.enabledFileTypes.includes(fileType);
  }

  /**
   * Add file type to enabled list
   */
  enableFileType(fileType: string): void {
    const enabledFileTypes = [...this.config.enabledFileTypes];
    if (!enabledFileTypes.includes(fileType)) {
      enabledFileTypes.push(fileType);
    }
    
    const disabledFileTypes = this.config.disabledFileTypes.filter(
      type => type !== fileType
    );
    
    this.updateConfig({ enabledFileTypes, disabledFileTypes });
  }

  /**
   * Add file type to disabled list
   */
  disableFileType(fileType: string): void {
    const disabledFileTypes = [...this.config.disabledFileTypes];
    if (!disabledFileTypes.includes(fileType)) {
      disabledFileTypes.push(fileType);
    }
    
    const enabledFileTypes = this.config.enabledFileTypes.filter(
      type => type !== fileType
    );
    
    this.updateConfig({ enabledFileTypes, disabledFileTypes });
  }

  /**
   * Subscribe to configuration changes
   */
  subscribe(listener: (config: AICompletionConfig) => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Validate configuration
   */
  validateConfig(config: Partial<AICompletionConfig>): string[] {
    const errors: string[] = [];
    
    if (config.debounceMs !== undefined && (config.debounceMs < 0 || config.debounceMs > 5000)) {
      errors.push('Debounce time must be between 0 and 5000ms');
    }
    
    if (config.maxSuggestions !== undefined && (config.maxSuggestions < 1 || config.maxSuggestions > 20)) {
      errors.push('Max suggestions must be between 1 and 20');
    }
    
    if (config.baseUrl !== undefined && !this.isValidUrl(config.baseUrl)) {
      errors.push('Base URL must be a valid URL');
    }
    
    return errors;
  }

  /**
   * Reset to default configuration
   */
  resetToDefaults(): void {
    this.config = { ...DEFAULT_CONFIG };
    this.saveConfig();
    this.notifyListeners();
  }

  /**
   * Export configuration for backup
   */
  exportConfig(): string {
    const exportData = {
      ...this.config,
      apiKey: '', // Don't export API key for security
      exportedAt: new Date().toISOString(),
    };
    return JSON.stringify(exportData, null, 2);
  }

  /**
   * Import configuration from backup
   */
  importConfig(configJson: string): void {
    try {
      const importedConfig = JSON.parse(configJson);
      delete importedConfig.exportedAt;
      delete importedConfig.apiKey; // Don't import API key
      
      const errors = this.validateConfig(importedConfig);
      if (errors.length > 0) {
        throw new Error(`Invalid configuration: ${errors.join(', ')}`);
      }
      
      this.updateConfig(importedConfig);
    } catch (error) {
      throw new Error(`Failed to import configuration: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Load configuration from storage
   */
  private loadConfig(): AICompletionConfig {
    if (typeof window === 'undefined') {
      return { ...DEFAULT_CONFIG };
    }
    
    try {
      const stored = localStorage.getItem(AICompletionConfigManager.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        const config = { ...DEFAULT_CONFIG, ...parsed };
        
        // Load API key separately
        config.apiKey = this.getApiKey();
        
        return config;
      }
    } catch (error) {
      console.error('Failed to load AI completion config:', error);
    }
    
    return { ...DEFAULT_CONFIG };
  }

  /**
   * Save configuration to storage
   */
  private saveConfig(): void {
    if (typeof window === 'undefined') return;
    
    try {
      const configToSave = { ...this.config };
      delete configToSave.apiKey; // Don't store API key with other config
      
      localStorage.setItem(
        AICompletionConfigManager.STORAGE_KEY,
        JSON.stringify(configToSave)
      );
    } catch (error) {
      console.error('Failed to save AI completion config:', error);
    }
  }

  /**
   * Notify all listeners of configuration changes
   */
  private notifyListeners(): void {
    const config = this.getConfig();
    this.listeners.forEach(listener => {
      try {
        listener(config);
      } catch (error) {
        console.error('Error in config listener:', error);
      }
    });
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

// Global instance
export const aiCompletionConfig = new AICompletionConfigManager();
