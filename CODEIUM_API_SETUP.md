# Codeium API Key Setup Guide

## Getting Your Free Individual API Key

### Step 1: Create Individual Account
1. Go to [https://codeium.com](https://codeium.com)
2. Click "Sign Up" (not "Sign Up for Teams")
3. Create an account with your personal email
4. Verify your email address

### Step 2: Generate API Key
1. After logging in, go to your account settings
2. Navigate to "API Keys" or "Integrations" section
3. Click "Generate New API Key"
4. Copy the generated key (it should look different from team keys)

### Step 3: Verify Key Type
- **Individual keys** typically start with different prefixes
- **Team keys** (like yours) often cause subscription errors
- Make sure you're logged into a personal account, not a team account

### Alternative: Use Codeium Extension Method
If the web interface doesn't work:

1. Install the official Codeium VS Code extension
2. Authenticate through the extension
3. The extension will generate a personal API key
4. Extract the key from VS Code settings or extension storage

### Troubleshooting Common Issues

#### Issue: "Team subscription not active"
- **Solution**: Use individual account, not team account
- **Check**: Make sure you're not logged into a team workspace

#### Issue: "Invalid API key format"
- **Solution**: Ensure you copied the complete key
- **Check**: Key should be a UUID format (like yours) but from individual account

#### Issue: "Rate limiting"
- **Solution**: Individual accounts have generous free limits
- **Check**: Wait a few minutes between requests during testing

### Free Tier Limits
- **Individual accounts**: Generous free usage
- **Rate limits**: Reasonable for development use
- **Features**: Full completion functionality
- **No credit card**: Required for free tier

### Security Best Practices
- Never commit API keys to version control
- Use environment variables in production
- Rotate keys periodically
- Monitor usage in Codeium dashboard
